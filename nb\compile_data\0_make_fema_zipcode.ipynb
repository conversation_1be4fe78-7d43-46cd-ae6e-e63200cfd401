{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# group FEMA dataset by (zipcode, year, month)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from astropy.table import Table\n", "import astropy.table as aTable\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["fema = Table.read('/Users/<USER>/data/noah/fema.trimed.csv', format='csv')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=2</i>\n", "<table id=\"table140243114033104\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>agricultureStructureIndicator</th><th>asOfDate</th><th>baseFloodElevation</th><th>basementEnclosureCrawlspace</th><th>reportedCity</th><th>condominiumIndicator</th><th>policyCount</th><th>countyCode</th><th>communityRatingSystemDiscount</th><th>dateOfLoss</th><th>elevatedBuildingIndicator</th><th>elevationCertificateIndicator</th><th>elevationDifference</th><th>censusTract</th><th>floodZone</th><th>houseWorship</th><th>latitude</th><th>longitude</th><th>locationOfContents</th><th>lowestAdjacentGrade</th><th>lowestFloorElevation</th><th>numberOfFloorsInTheInsuredBuilding</th><th>nonProfitIndicator</th><th>obstructionType</th><th>occupancyType</th><th>originalConstructionDate</th><th>originalNBDate</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>amountPaidOnIncreasedCostOfComplianceClaim</th><th>postFIRMConstructionIndicator</th><th>rateMethod</th><th>smallBusinessIndicatorBuilding</th><th>state</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>yearOfLoss</th><th>reportedZipcode</th><th>primaryResidence</th><th>id</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th></tr></thead>\n", "<thead><tr><th>int64</th><th>str24</th><th>int64</th><th>int64</th><th>str30</th><th>str1</th><th>int64</th><th>int64</th><th>int64</th><th>str24</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>str3</th><th>int64</th><th>float64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>str24</th><th>str24</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>str1</th><th>int64</th><th>str2</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>str24</th><th>float64</th><th>int64</th></tr></thead>\n", "<tr><td>0</td><td>2020-08-23T02:35:07.366Z</td><td>9</td><td>--</td><td>ST PETERSBURG</td><td>N</td><td>1</td><td>12103</td><td>5</td><td>1996-10-08T04:00:00.000Z</td><td>0</td><td>3</td><td>93</td><td>--</td><td>AE</td><td>0</td><td>27.8</td><td>-82.6</td><td>--</td><td>--</td><td>101</td><td>1</td><td>0</td><td>10</td><td>1</td><td>1959-07-23T04:00:00.000Z</td><td>1995-03-29T05:00:00.000Z</td><td>21567.68</td><td>32800.0</td><td>--</td><td>0</td><td>1</td><td>0</td><td>FL</td><td>65500</td><td>32800</td><td>1996</td><td>33703</td><td>1</td><td>5f6a41bce1e65249b3237e5f</td><td>54367.68</td><td>98300</td></tr>\n", "<tr><td>0</td><td>2020-08-23T02:35:07.366Z</td><td>--</td><td>--</td><td>RIVER RIDGE</td><td>N</td><td>1</td><td>22051</td><td>5</td><td>1995-05-08T04:00:00.000Z</td><td>0</td><td>--</td><td>--</td><td>--</td><td>X</td><td>0</td><td>30.0</td><td>-90.2</td><td>--</td><td>--</td><td>--</td><td>1</td><td>0</td><td>10</td><td>1</td><td>1960-06-15T04:00:00.000Z</td><td>1995-03-31T05:00:00.000Z</td><td>18987.7</td><td>5897.3</td><td>--</td><td>0</td><td>7</td><td>0</td><td>LA</td><td>75000</td><td>18000</td><td>1995</td><td>70123</td><td>0</td><td>5f6a41bce1e65249b3237e60</td><td>24885.0</td><td>93000</td></tr>\n", "</table>"], "text/plain": ["<Table length=2>\n", "agricultureStructureIndicator ... totalTotalInsuranceCoverage\n", "            int64             ...            int64           \n", "----------------------------- ... ---------------------------\n", "                            0 ...                       98300\n", "                            0 ...                       93000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["fema[:2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CRS treatment status\n", "- CRS scores <= 9 will be considered treated\n", "- CRS score > 9 will be considered control\n", "\n", "`crs_treat` will be used to estimate the average CRS treatment status for a zipcode"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# CRS scores <= 9 will be considered treated\n", "crs_treat = np.zeros(len(fema)).astype(bool)\n", "crs_treat[fema['communityRatingSystemDiscount'] <= 9] = True"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["fema['crs_treat'] = crs_treat"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# remove entries without zipcodes\n", "**we should check that the removed entries aren't biased in some what**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["44718 of 1916033 entries do not have zipcodes\n", "2.33 percent\n"]}], "source": ["print('%i of %i entries do not have zipcodes' % (np.sum(fema['reportedZipcode'].mask), len(fema['reportedZipcode'])))\n", "print('%.2f percent' % (np.mean(fema['reportedZipcode'].mask)*100.))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["fema = fema[~fema['reportedZipcode'].mask]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["weird = (\n", "    ((fema['reportedZipcode'] == 77571) & (fema['state'] == 'NJ')) | \n", "    (fema['reportedZipcode'] == 0) |\n", "    (fema['reportedZipcode'] == 99999)\n", ")\n", "fema = fema[~weird]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["23 of 1871315 are weird\n", "0.00 percent\n"]}], "source": ["print('%i of %i are weird' % (np.sum(weird), len(weird)))\n", "print('%.2f percent' % (np.mean(weird) * 100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# group by [zipcode, yearOfLoss, monthOfLoss]\n", "State is mainly to take care of spurious zipcodes"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["months = np.array([int(date.split('-')[1]) for date in fema['dateOfLoss']])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["fema['monthOfLoss'] = months"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["fema_zip = fema['reportedZipcode', 'yearOfLoss', 'monthOfLoss', 'state', 'longitude', 'latitude', 'crs_treat', 'communityRatingSystemDiscount', 'primaryResidence'].group_by(['reportedZipcode', 'yearOfLoss', 'monthOfLoss'])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# fill in missing longitude or latitude\n", "N_nolonglat = 0\n", "for group in fema_zip.groups:\n", "    if np.sum(group['longitude'].mask) < len(group):\n", "        group['longitude'][group['longitude'].mask] = group['longitude'][~group['longitude'].mask][0]\n", "        group['latitude'][group['latitude'].mask] = group['latitude'][~group['latitude'].mask][0]            \n", "    elif np.sum(~group['longitude'].mask) == 0: \n", "        N_nolonglat += 1"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4093 or 1871292 entries do not have long,lat data\n", "0.22 percent\n"]}], "source": ["print('%i or %i entries do not have long,lat data' % (<PERSON>_<PERSON><PERSON><PERSON>, len(fema_zip))) \n", "print('%.2f percent'% (100*float(N_nolonglat) / float(len(fema_zip))))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# manual corrections to zipcode -- state mismatch \n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 10011) & (fema_zip['state'] == 'LA')] = 'NY'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 10035) & (fema_zip['state'] == 'VA')] = 'NY'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 11720) & (fema_zip['state'] == 'PA')] = 'NY'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 11721) & (fema_zip['state'] == 'PA')] = 'NY'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 27546) & (fema_zip['state'] == 'WV')] = 'NC'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 31602) & (fema_zip['state'] == 'FL')] = 'GA'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 37064) & (fema_zip['state'] == 'KY')] = 'TN'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 38127) & (fema_zip['state'] == 'TX')] = 'TN'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 38901) & (fema_zip['state'] == 'MI')] = 'MS'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 39307) & (fema_zip['state'] == 'AL')] = 'MS'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 56219) & (fema_zip['state'] == 'SD')] = 'MN'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 58105) & (fema_zip['state'] == 'WI')] = 'ND'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 63633) & (fema_zip['state'] == 'OH')] = 'MO'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 5155) & (fema_zip['state'] == 'UT')] = 'VT'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 10000) & (fema_zip['state'] == 'PA')] = 'NY'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 19342) & (fema_zip['state'] == 'NJ')] = 'PA'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 65251) & (fema_zip['state'] == 'MI')] = 'MO'\n", "fema_zip['state'][(fema_zip['reportedZipcode'] == 70535) & (fema_zip['state'] == 'MA')] = 'LA'"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# fix state mismatches\n", "for group in fema_zip.groups: \n", "    if np.sum(group['state'] != group['state'][0]): \n", "        is_un = (group['state'] == 'UN')\n", "        group['state'][is_un] = group['state'][~is_un][0]\n", "        \n", "        uniq_state, n_state = np.unique(group['state'], return_counts=True)\n", "        assert np.sum(n_state == n_state[np.argmax(n_state)]) == 1, group['reportedZipcode', 'state']\n", "        group['state'][group['state'] != uniq_state[np.argmax(n_state)]] = uniq_state[np.argmax(n_state)]\n", "        \n", "        assert np.sum(group['state'] != group['state'][0]) == 0, group"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["fema_zip = fema_zip.group_by(['reportedZipcode', 'yearOfLoss', 'monthOfLoss', 'state'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# check that all the states match \n", "for group in fema_zip.groups: \n", "    assert np.sum(group['state'] != group['state'][0]) == 0"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/noah/lib/python3.7/site-packages/astropy/table/groups.py:259: UserWarning: Warning: converting a masked element to nan.\n", "  vals = np.array([func(par_col[i0: i1]) for i0, i1 in zip(i0s, i1s)])\n"]}], "source": ["# combine each group with mean\n", "fema_zip_group = fema_zip.groups.aggregate(np.mean)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=15</i>\n", "<table id=\"table140249522336528\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th></tr></thead>\n", "<tr><td>601</td><td>1992</td><td>1</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>0.0</td></tr>\n", "<tr><td>4063</td><td>1996</td><td>10</td><td>ME</td><td>-70.4</td><td>43.5</td><td>1.0</td><td>7.0</td><td>0.4666666666666667</td></tr>\n", "<tr><td>7946</td><td>1978</td><td>1</td><td>NJ</td><td>-74.5</td><td>40.7</td><td>0.5</td><td>9.0</td><td>0.0</td></tr>\n", "<tr><td>11362</td><td>1987</td><td>7</td><td>NY</td><td>-73.7</td><td>40.800000000000004</td><td>0.0</td><td>11.0</td><td>0.0</td></tr>\n", "<tr><td>14738</td><td>1998</td><td>1</td><td>NY</td><td>-79.2</td><td>42.0</td><td>0.0</td><td>11.0</td><td>1.0</td></tr>\n", "<tr><td>19010</td><td>1999</td><td>9</td><td>PA</td><td>-75.4</td><td>40.0</td><td>0.0</td><td>11.0</td><td>1.0</td></tr>\n", "<tr><td>23601</td><td>2012</td><td>10</td><td>VA</td><td>-76.5</td><td>37.0</td><td>0.0</td><td>11.0</td><td>1.0</td></tr>\n", "<tr><td>28205</td><td>2003</td><td>11</td><td>NC</td><td>-80.8</td><td>35.2</td><td>1.0</td><td>4.0</td><td>1.0</td></tr>\n", "<tr><td>32019</td><td>1982</td><td>6</td><td>FL</td><td>-81.0</td><td>29.133333333333336</td><td>1.0</td><td>5.0</td><td>0.0</td></tr>\n", "<tr><td>33462</td><td>1982</td><td>3</td><td>FL</td><td>-80.08181818181818</td><td>26.6</td><td>1.0</td><td>7.0</td><td>0.0</td></tr>\n", "<tr><td>36606</td><td>2015</td><td>12</td><td>AL</td><td>-88.1</td><td>30.7</td><td>0.0</td><td>10.0</td><td>0.0</td></tr>\n", "<tr><td>40065</td><td>2019</td><td>7</td><td>KY</td><td>-85.3</td><td>38.2</td><td>0.0</td><td>11.0</td><td>1.0</td></tr>\n", "<tr><td>46064</td><td>1992</td><td>6</td><td>IN</td><td>-85.8</td><td>40.0</td><td>0.0</td><td>11.0</td><td>0.0</td></tr>\n", "<tr><td>53930</td><td>2013</td><td>5</td><td>WI</td><td>-89.5</td><td>43.7</td><td>0.0</td><td>11.0</td><td>1.0</td></tr>\n", "<tr><td>61032</td><td>2017</td><td>7</td><td>IL</td><td>-89.60588235294117</td><td>42.305882352941175</td><td>0.0</td><td>11.0</td><td>0.47058823529411764</td></tr>\n", "</table>"], "text/plain": ["<Table length=15>\n", "reportedZipcode yearOfLoss ... communityRatingSystemDiscount   primaryResidence \n", "     int64        int64    ...            float64                  float64      \n", "--------------- ---------- ... ----------------------------- -------------------\n", "            601       1992 ...                          11.0                 0.0\n", "           4063       1996 ...                           7.0  0.4666666666666667\n", "           7946       1978 ...                           9.0                 0.0\n", "          11362       1987 ...                          11.0                 0.0\n", "          14738       1998 ...                          11.0                 1.0\n", "          19010       1999 ...                          11.0                 1.0\n", "          23601       2012 ...                          11.0                 1.0\n", "          28205       2003 ...                           4.0                 1.0\n", "          32019       1982 ...                           5.0                 0.0\n", "          33462       1982 ...                           7.0                 0.0\n", "          36606       2015 ...                          10.0                 0.0\n", "          40065       2019 ...                          11.0                 1.0\n", "          46064       1992 ...                          11.0                 0.0\n", "          53930       2013 ...                          11.0                 1.0\n", "          61032       2017 ...                          11.0 0.47058823529411764"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["fema_zip_group[::10000][:15]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["fema_zip_insurance = fema['reportedCity', \n", "                          'reportedZipcode', \n", "                          'yearOfLoss', \n", "                          'monthOfLoss',  \n", "                          'policyCount', \n", "                          'amountPaidOnBuildingClaim', \n", "                          'amountPaidOnContentsClaim', \n", "                          'totalBuildingInsuranceCoverage', \n", "                          'totalContentsInsuranceCoverage', \n", "                          'amountPaidOnTotalClaim', \n", "                          'totalTotalInsuranceCoverage'].group_by(['reportedZipcode', 'yearOfLoss', 'monthOfLoss'])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING: Cannot aggregate column 'reportedCity' with type '<U30' [astropy.table.groups]\n"]}], "source": ["# add up all the insurance claims for the zipcode per year\n", "fema_zip_insurance_group = fema_zip_insurance.groups.aggregate(np.sum)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=15</i>\n", "<table id=\"table140249522337936\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th></tr></thead>\n", "<tr><td>601</td><td>1992</td><td>1</td><td>1.0</td><td>1106.92</td><td>0.0</td><td>14600</td><td>0</td><td>1106.92</td><td>14600</td></tr>\n", "<tr><td>4063</td><td>1996</td><td>10</td><td>15.0</td><td>96808.1</td><td>20319.410000000003</td><td>1291400</td><td>249500</td><td>117127.51000000001</td><td>1540900</td></tr>\n", "<tr><td>7946</td><td>1978</td><td>1</td><td>2.0</td><td>452.95</td><td>270.0</td><td>17300</td><td>2800</td><td>722.95</td><td>20100</td></tr>\n", "<tr><td>11362</td><td>1987</td><td>7</td><td>6.0</td><td>19901.72</td><td>0.0</td><td>144000</td><td>43100</td><td>19901.72</td><td>187100</td></tr>\n", "<tr><td>14738</td><td>1998</td><td>1</td><td>1.0</td><td>4532.0</td><td>0.0</td><td>68200</td><td>0</td><td>4532.0</td><td>68200</td></tr>\n", "<tr><td>19010</td><td>1999</td><td>9</td><td>2.0</td><td>14410.16</td><td>820.25</td><td>200800</td><td>19800</td><td>15230.41</td><td>220600</td></tr>\n", "<tr><td>23601</td><td>2012</td><td>10</td><td>1.0</td><td>4123.64</td><td>0.0</td><td>234000</td><td>11600</td><td>4123.64</td><td>245600</td></tr>\n", "<tr><td>28205</td><td>2003</td><td>11</td><td>1.0</td><td>4430.83</td><td>0.0</td><td>30000</td><td>8000</td><td>4430.83</td><td>38000</td></tr>\n", "<tr><td>32019</td><td>1982</td><td>6</td><td>3.0</td><td>1873.1399999999999</td><td>0.0</td><td>173100</td><td>24100</td><td>1873.1399999999999</td><td>197200</td></tr>\n", "<tr><td>33462</td><td>1982</td><td>3</td><td>11.0</td><td>20514.649999999998</td><td>5644.55</td><td>827100</td><td>284300</td><td>26159.2</td><td>1111400</td></tr>\n", "<tr><td>36606</td><td>2015</td><td>12</td><td>1.0</td><td>438.74</td><td>0.0</td><td>9600</td><td>12800</td><td>438.74</td><td>22400</td></tr>\n", "<tr><td>40065</td><td>2019</td><td>7</td><td>1.0</td><td>1801.76</td><td>0.0</td><td>250000</td><td>100000</td><td>1801.76</td><td>350000</td></tr>\n", "<tr><td>46064</td><td>1992</td><td>6</td><td>1.0</td><td>1955.67</td><td>0.0</td><td>60000</td><td>60000</td><td>1955.67</td><td>120000</td></tr>\n", "<tr><td>53930</td><td>2013</td><td>5</td><td>1.0</td><td>3277.34</td><td>0.0</td><td>150000</td><td>60000</td><td>3277.34</td><td>210000</td></tr>\n", "<tr><td>61032</td><td>2017</td><td>7</td><td>17.0</td><td>499289.73</td><td>146930.34</td><td>1905800</td><td>730600</td><td>646220.0700000001</td><td>2636400</td></tr>\n", "</table>"], "text/plain": ["<Table length=15>\n", "reportedZipcode yearOfLoss ... totalTotalInsuranceCoverage\n", "     int64        int64    ...            int64           \n", "--------------- ---------- ... ---------------------------\n", "            601       1992 ...                       14600\n", "           4063       1996 ...                     1540900\n", "           7946       1978 ...                       20100\n", "          11362       1987 ...                      187100\n", "          14738       1998 ...                       68200\n", "          19010       1999 ...                      220600\n", "          23601       2012 ...                      245600\n", "          28205       2003 ...                       38000\n", "          32019       1982 ...                      197200\n", "          33462       1982 ...                     1111400\n", "          36606       2015 ...                       22400\n", "          40065       2019 ...                      350000\n", "          46064       1992 ...                      120000\n", "          53930       2013 ...                      210000\n", "          61032       2017 ...                     2636400"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["fema_zip_insurance_group[::10000][:15]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["fema_zip_comb = aTable.join(fema_zip_group, fema_zip_insurance_group, keys=['reportedZipcode', 'yearOfLoss', 'monthOfLoss'], join_type='left')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=213680</i>\n", "<table id=\"table140249210353168\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th></tr></thead>\n", "<tr><td>601</td><td>1992</td><td>1</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>1106.92</td><td>0.0</td><td>14600</td><td>0</td><td>1106.92</td><td>14600</td></tr>\n", "<tr><td>601</td><td>1992</td><td>10</td><td>PR</td><td>nan</td><td>nan</td><td>0.0</td><td>11.0</td><td>0.0</td><td>2.0</td><td>764.89</td><td>4731.0</td><td>14000</td><td>9000</td><td>5495.889999999999</td><td>23000</td></tr>\n", "<tr><td>601</td><td>1993</td><td>5</td><td>PR</td><td>nan</td><td>nan</td><td>0.0</td><td>11.0</td><td>0.0</td><td>4.0</td><td>3131.57</td><td>6839.5</td><td>14000</td><td>30500</td><td>9971.07</td><td>44500</td></tr>\n", "<tr><td>601</td><td>1998</td><td>9</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>32150.0</td><td>0.0</td><td>500000</td><td>0</td><td>32150.0</td><td>500000</td></tr>\n", "<tr><td>601</td><td>2003</td><td>11</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>1.0</td><td>2.0</td><td>4719.68</td><td>3318.5</td><td>20000</td><td>7800</td><td>8038.179999999999</td><td>27800</td></tr>\n", "<tr><td>601</td><td>2008</td><td>9</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>3821.27</td><td>1989.0</td><td>10600</td><td>15000</td><td>5810.27</td><td>25600</td></tr>\n", "<tr><td>601</td><td>2008</td><td>10</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>2360.14</td><td>1117.0</td><td>10600</td><td>15000</td><td>3477.14</td><td>25600</td></tr>\n", "<tr><td>601</td><td>2017</td><td>9</td><td>PR</td><td>-66.7</td><td>18.2</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>8540.26</td><td>25424.25</td><td>17300</td><td>100000</td><td>33964.51</td><td>117300</td></tr>\n", "<tr><td>602</td><td>1988</td><td>4</td><td>PR</td><td>nan</td><td>nan</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>20824.0</td><td>0</td><td>50000</td><td>20824.0</td><td>50000</td></tr>\n", "<tr><td>602</td><td>1988</td><td>8</td><td>PR</td><td>nan</td><td>nan</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>18232.0</td><td>0</td><td>50000</td><td>18232.0</td><td>50000</td></tr>\n", "<tr><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td><td>...</td></tr>\n", "<tr><td>99801</td><td>2009</td><td>11</td><td>AK</td><td>-134.4</td><td>58.3</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>72626.62</td><td>0.0</td><td>250000</td><td>0</td><td>72626.62</td><td>250000</td></tr>\n", "<tr><td>99801</td><td>2011</td><td>7</td><td>AK</td><td>-134.6</td><td>58.4</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>18895.02</td><td>4161.98</td><td>250000</td><td>93100</td><td>23057.0</td><td>343100</td></tr>\n", "<tr><td>99801</td><td>2014</td><td>7</td><td>AK</td><td>-134.6</td><td>58.4</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>173311.69</td><td>31325.37</td><td>250000</td><td>93100</td><td>204637.06</td><td>343100</td></tr>\n", "<tr><td>99801</td><td>2014</td><td>8</td><td>AK</td><td>-134.6</td><td>58.4</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>13093.45</td><td>0.0</td><td>250000</td><td>93100</td><td>13093.45</td><td>343100</td></tr>\n", "<tr><td>99801</td><td>2017</td><td>10</td><td>AK</td><td>-134.6</td><td>58.4</td><td>0.0</td><td>11.0</td><td>0.5</td><td>2.0</td><td>31962.91</td><td>0.0</td><td>500000</td><td>0</td><td>31962.91</td><td>500000</td></tr>\n", "<tr><td>99824</td><td>2003</td><td>12</td><td>AK</td><td>-134.4</td><td>58.3</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>4899.73</td><td>0.0</td><td>200000</td><td>50000</td><td>4899.73</td><td>250000</td></tr>\n", "<tr><td>99835</td><td>2009</td><td>1</td><td>AK</td><td>-135.3</td><td>57.0</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>20130.27</td><td>0.0</td><td>88200</td><td>0</td><td>20130.27</td><td>88200</td></tr>\n", "<tr><td>99901</td><td>1980</td><td>5</td><td>AK</td><td>-131.7</td><td>55.4</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>15000.0</td><td>7500.0</td><td>15000</td><td>7500</td><td>22500.0</td><td>22500</td></tr>\n", "<tr><td>99901</td><td>2015</td><td>1</td><td>AK</td><td>-131.6</td><td>55.3</td><td>0.0</td><td>10.0</td><td>0.5</td><td>2.0</td><td>23885.920000000002</td><td>0.0</td><td>478300</td><td>50000</td><td>23885.920000000002</td><td>528300</td></tr>\n", "<tr><td>99901</td><td>2015</td><td>10</td><td>AK</td><td>-131.7</td><td>55.4</td><td>0.0</td><td>10.0</td><td>0.0</td><td>1.0</td><td>49346.45</td><td>25804.1</td><td>500000</td><td>50000</td><td>75150.54999999999</td><td>550000</td></tr>\n", "</table>"], "text/plain": ["<Table length=213680>\n", "reportedZipcode yearOfLoss ... totalTotalInsuranceCoverage\n", "     int64        int64    ...            int64           \n", "--------------- ---------- ... ---------------------------\n", "            601       1992 ...                       14600\n", "            601       1992 ...                       23000\n", "            601       1993 ...                       44500\n", "            601       1998 ...                      500000\n", "            601       2003 ...                       27800\n", "            601       2008 ...                       25600\n", "            601       2008 ...                       25600\n", "            601       2017 ...                      117300\n", "            602       1988 ...                       50000\n", "            602       1988 ...                       50000\n", "            ...        ... ...                         ...\n", "          99801       2009 ...                      250000\n", "          99801       2011 ...                      343100\n", "          99801       2014 ...                      343100\n", "          99801       2014 ...                      343100\n", "          99801       2017 ...                      500000\n", "          99824       2003 ...                      250000\n", "          99835       2009 ...                       88200\n", "          99901       1980 ...                       22500\n", "          99901       2015 ...                      528300\n", "          99901       2015 ...                      550000"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["fema_zip_comb"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["fema_zip_comb.write('/Users/<USER>/data/noah/fema.zipcode.csv', format='csv', overwrite=True)"]}], "metadata": {"kernelspec": {"display_name": "noah", "language": "python", "name": "noah"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}