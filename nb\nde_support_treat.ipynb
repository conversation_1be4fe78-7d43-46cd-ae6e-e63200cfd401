{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# train $\\mathcal{Q}_T(X)$ to estimate covariate support for treated sample"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os, glob\n", "import numpy as np\n", "import astropy.table as aTable\n", "from tqdm.notebook import tqdm, trange"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import copy\n", "import torch\n", "from nflows import transforms, distributions, flows\n", "from torch import optim"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import corner as DFM\n", "# --- plotting ---\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "mpl.rcParams['text.usetex'] = True\n", "mpl.rcParams['font.family'] = 'serif'\n", "mpl.rcParams['axes.linewidth'] = 1.5\n", "mpl.rcParams['axes.xmargin'] = 1\n", "mpl.rcParams['xtick.labelsize'] = 'x-large'\n", "mpl.rcParams['xtick.major.size'] = 5\n", "mpl.rcParams['xtick.major.width'] = 1.5\n", "mpl.rcParams['ytick.labelsize'] = 'x-large'\n", "mpl.rcParams['ytick.major.size'] = 5\n", "mpl.rcParams['ytick.major.width'] = 1.5\n", "mpl.rcParams['legend.frameon'] = False"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["if torch.cuda.is_available(): device = 'cuda'\n", "else: device = 'cpu'"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["treat_data = np.load('../dat/fema_crs.treat.train.npy')\n", "\n", "Ntrain = int(0.9 * treat_data.shape[0])\n", "itests = np.random.choice(np.arange(treat_data.shape[0]), size=treat_data.shape[0] - Ntrain, replace=False)\n", "is_test = np.zeros(treat_data.shape[0]).astype(bool)\n", "is_test[itests] = True\n", "\n", "train_x = treat_data[:,1:]\n", "\n", "ndim = train_x.shape[1]\n", "train_loader = torch.utils.data.DataLoader(\n", "        torch.tensor(train_x[~is_test].astype(np.float32)).to(device), \n", "        batch_size=512, shuffle=True)\n", "valid_loader = torch.utils.data.DataLoader(\n", "        torch.tensor(train_x[is_test].astype(np.float32)).to(device), \n", "        batch_size=512, shuffle=False)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["122x3\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "adcb30c3920c42c1bf43c2ae88e506c4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["88x4\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["95x5\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "700a5867ba0b46509af914d390fac1ba", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/sbi/lib/python3.7/site-packages/ipykernel_launcher.py:59: UserWarning: Attempting to set identical left == right == 0 results in singular transformations; automatically expanding.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["87x3\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ba107af5609b452f9ff99f78b777bd6e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["105x5\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffba318a39434ab19eba91836a87fcca", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["num_iter = 1000\n", "patience = 20\n", "lrate   = 1e-3\n", "\n", "all_flows, all_archs, all_valid_losses = [], [], []\n", "for i in range(5): \n", "    nhidden = int(np.ceil(np.random.uniform(64, 128)))\n", "    nblocks = int(np.random.uniform(3, 7))\n", "    print('%ix%i' % (nhidden, nblocks))\n", "    blocks = []\n", "    for iblock in range(nblocks): \n", "        blocks += [transforms.MaskedAffineAutoregressiveTransform(features=ndim, hidden_features=nhidden),\n", "                transforms.RandomPermutation(features=ndim)]\n", "    transform = transforms.CompositeTransform(blocks)\n", "\n", "    base_distribution = distributions.StandardNormal(shape=[ndim])\n", "    flow = flows.Flow(transform=transform, distribution=base_distribution)\n", "    flow.to(device)\n", "\n", "    optimizer = optim.Adam(flow.parameters(), lr=lrate)\n", "    scheduler = scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=patience, min_lr=1e-5)\n", "    #optim.lr_scheduler.OneCycleLR(optimizer, lrate, total_steps=num_iter)\n", "\n", "    best_epoch, best_valid_loss, valid_losses = 0, np.inf, []\n", "    t = trange(num_iter, leave=False)\n", "    for epoch in t:\n", "        train_loss = 0.\n", "        for batch in train_loader: \n", "            optimizer.zero_grad()\n", "            loss = -flow.log_prob(batch).mean()\n", "            loss.backward()\n", "            train_loss += loss.item()\n", "            optimizer.step()\n", "        train_loss = train_loss/float(len(train_loader))\n", "\n", "        with torch.no_grad():\n", "            valid_loss = 0.\n", "            for batch in valid_loader: \n", "                loss = -flow.log_prob(batch).mean()\n", "                valid_loss += loss.item()\n", "            valid_loss = valid_loss/len(valid_loader)\n", "            if np.isnan(valid_loss): break\n", "            valid_losses.append(valid_loss)\n", "        scheduler.step(valid_loss)\n", "\n", "        t.set_description('Epoch: %i LR %.2e TRAINING Loss: %.2e VALIDATION Loss: %.2e' % \n", "                          (epoch, scheduler._last_lr[0], train_loss, valid_loss), refresh=False)            \n", "            \n", "        if scheduler._last_lr[0] == 1e-5: \n", "            if valid_loss < best_valid_loss: \n", "                best_valid_loss = valid_loss\n", "                best_epoch = epoch\n", "                best_flow = copy.deepcopy(flow)\n", "            else: \n", "                if epoch > best_epoch + patience: \n", "                    break \n", "                    \n", "    plt.plot(np.arange(len(valid_losses)), valid_losses)\n", "    plt.xlim(0, len(valid_losses))\n", "    plt.show()\n", "    \n", "    all_archs.append('%ix%i' % (nhidden, nblocks))\n", "    all_flows.append(best_flow)\n", "    all_valid_losses.append(valid_losses)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["_losses = []\n", "for vl in all_valid_losses:\n", "    if len(vl) > 0: _losses.append(np.nanmin(vl))\n", "    else: _losses.append(np.inf)\n", "ibest = np.nanargmin(_losses)\n", "\n", "best_flow = all_flows[ibest]\n", "best_arch = all_archs[ibest]\n", "\n", "torch.save(best_flow, '../dat/qphi_support.v2.log.treat.pt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# validate $\\mathcal{Q}_T(X)$ against $p_T(X)$"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["nde_covar = best_flow.sample(10000)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 698.4x698.4 with 16 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = DFM.corner(treat_data[:,1:], color='k', hist_kwargs={'density': True}, \n", "                 labels=['Mean Rainfall', 'Risk Score', 'log Household Income', 'log Household Number'],\n", "                 label_kwargs={'fontsize': 15}, plot_datapoints=False, plot_density=False, smooth=False)\n", "fig = DFM.corner(np.array(nde_covar.detach().cpu()), color='C1', hist_kwargs={'density': True}, \n", "                 range=[(np.min(col), np.max(col)) for col in treat_data[:,1:].T],\n", "                 labels=['Mean Rainfall', 'Risk Score', 'log Household Income', 'log Household Number'],\n", "                 label_kwargs={'fontsize': 15}, plot_datapoints=False, plot_density=False, smooth=False, fig=fig)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sbi", "language": "python", "name": "sbi"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.10"}}, "nbformat": 4, "nbformat_minor": 5}