{"cells": [{"cell_type": "markdown", "id": "4678f207", "metadata": {}, "source": ["# validate $q^C_\\phi(Y\\,|\\,X)$ and $q^T_\\phi(Y\\,|\\,X)$ using test data"]}, {"cell_type": "code", "execution_count": 1, "id": "8c3d58b3", "metadata": {}, "outputs": [], "source": ["import os, glob\n", "import numpy as np\n", "import astropy.table as aTable\n", "from tqdm.notebook import tqdm, trange"]}, {"cell_type": "code", "execution_count": 2, "id": "daaf77e2", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 3, "id": "f7ed2827", "metadata": {}, "outputs": [], "source": ["from tarp import get_drp_coverage"]}, {"cell_type": "code", "execution_count": 4, "id": "1f9461dd", "metadata": {}, "outputs": [], "source": ["import corner as DFM\n", "# --- plotting ---\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "mpl.rcParams['text.usetex'] = True\n", "mpl.rcParams['font.family'] = 'serif'\n", "mpl.rcParams['axes.linewidth'] = 1.5\n", "mpl.rcParams['axes.xmargin'] = 1\n", "mpl.rcParams['xtick.labelsize'] = 'x-large'\n", "mpl.rcParams['xtick.major.size'] = 5\n", "mpl.rcParams['xtick.major.width'] = 1.5\n", "mpl.rcParams['ytick.labelsize'] = 'x-large'\n", "mpl.rcParams['ytick.major.size'] = 5\n", "mpl.rcParams['ytick.major.width'] = 1.5\n", "mpl.rcParams['legend.frameon'] = False"]}, {"cell_type": "code", "execution_count": 4, "id": "ac5b30ff", "metadata": {}, "outputs": [], "source": ["if torch.cuda.is_available(): device = 'cuda'\n", "else: device = 'cpu'"]}, {"cell_type": "markdown", "id": "78789cdb", "metadata": {}, "source": ["## read compiled dataset"]}, {"cell_type": "code", "execution_count": 5, "id": "6dc890dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["74288 entries; 14729 unique zipcodes\n"]}], "source": ["data = aTable.Table.read('/scratch/gpfs/chhahn/noah/zipcode.fema.fsf.acs.rainfall.v2.csv', format='csv')\n", "print('%i entries; %i unique zipcodes' % (len(data), len(np.unique(data['reportedZipcode']))))"]}, {"cell_type": "code", "execution_count": 6, "id": "5b85b1d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["43644 entries in control; 11258 unique zipcodes\n", "30644 entries in control; 5040 unique zipcodes\n"]}], "source": ["control = (data['communityRatingSystemDiscount'] == 11.)\n", "print(f\"{np.sum(control)} entries in control; {len(np.unique(data['reportedZipcode'][control]))} unique zipcodes\")\n", "\n", "treat = (data['communityRatingSystemDiscount'] < 11.)\n", "print(f\"{np.sum(treat)} entries in control; {len(np.unique(data['reportedZipcode'][treat]))} unique zipcodes\")"]}, {"cell_type": "code", "execution_count": 7, "id": "552700c2", "metadata": {}, "outputs": [], "source": ["columns = ['amountPaidOnTotalClaim_per_policy', 'mean_rainfall', 'avg_risk_score_all', \n", "           'median_household_income', 'population', 'renter_fraction', 'educated_fraction', 'white_fraction'] "]}, {"cell_type": "code", "execution_count": 8, "id": "66dd7061", "metadata": {}, "outputs": [], "source": ["control_data = np.vstack([np.ma.getdata(data[col][control].data) for col in columns]).T\n", "control_data[:,0] = np.log10(control_data[:,0])\n", "control_data[:,3] = np.log10(control_data[:,3])\n", "control_data[:,4] = np.log10(control_data[:,4])\n", "\n", "treat_data = np.vstack([np.ma.getdata(data[col][treat].data) for col in columns]).T\n", "treat_data[:,0] = np.log10(treat_data[:,0])\n", "treat_data[:,3] = np.log10(treat_data[:,3])\n", "treat_data[:,4] = np.log10(treat_data[:,4])"]}, {"cell_type": "markdown", "id": "e95eec1b", "metadata": {}, "source": ["## read $q^C_\\phi(Y\\,|\\,X)$ and $q^T_\\phi(Y\\,|\\,X)$"]}, {"cell_type": "code", "execution_count": 10, "id": "fcfcbc12", "metadata": {}, "outputs": [], "source": ["def read_qphi(nde_name):\n", "    qphis = []\n", "    for fqphi in glob.glob('../dat/nde/%s/*.pt' % nde_name): \n", "        qphi = torch.load(fqphi, map_location=device)\n", "        qphis.append(qphi)\n", "    return qphis"]}, {"cell_type": "code", "execution_count": 11, "id": "2da14c77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2313 models trained\n", "[680, 1365, 1087, 78, 7]\n", "2609 models trained\n", "[462, 1287, 1547, 2243, 269]\n"]}], "source": ["qphis_control = read_qphi('qphi_control')\n", "qphis_treat = read_qphi('qphi_treat')"]}, {"cell_type": "markdown", "id": "3e1ead66", "metadata": {}, "source": ["## validate $q^C_\\phi(Y\\,|\\,X)$ and $q^T_\\phi(Y\\,|\\,X)$ using DRP coverage test\n", "See [<PERSON><PERSON> et al (2023)](https://ui.adsabs.harvard.edu/abs/2023PMLR..20219256L/abstract) for details on the DRP coverage test."]}, {"cell_type": "code", "execution_count": 37, "id": "b50bcfd0", "metadata": {}, "outputs": [], "source": ["i_test_control = np.random.choice(np.arange(control_data.shape[0]), size=int(control_data.shape[0] * 0.1))\n", "i_test_treat = np.random.choice(np.arange(treat_data.shape[0]), size=int(treat_data.shape[0] * 0.1))"]}, {"cell_type": "code", "execution_count": 38, "id": "70d295c5", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a67c91d623db43688c2cfa2fba5ee9e5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/4364 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1aac83ff632f45d0a45e80b0ec07a7df", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3064 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n_sample = 10000\n", "\n", "y_nde_control = []\n", "for i in tqdm(i_test_control): \n", "    y_samp = []\n", "    for qphi_control in qphis_control: \n", "        _samp = qphi_control.sample((int(n_sample/len(qphis_treat)),), \n", "                                    x=torch.tensor(control_data[i,1:], dtype=torch.float32).to(device), \n", "                                    show_progress_bars=False)\n", "        y_samp.append(_samp.detach().cpu().numpy())\n", "\n", "    y_nde_control.append(np.concatenate(np.array(y_samp), axis=0))        \n", "y_nde_control = np.array(y_nde_control)\n", "\n", "\n", "y_nde_treat = []\n", "for i in tqdm(i_test_treat): \n", "    y_samp = []\n", "    for qphi_treat in qphis_treat: \n", "        _samp = qphi_treat.sample((int(n_sample/len(qphis_treat)),), \n", "                                    x=torch.tensor(treat_data[i,1:], dtype=torch.float32).to(device), \n", "                                    show_progress_bars=False)\n", "        y_samp.append(_samp.detach().cpu().numpy())\n", "\n", "    y_nde_treat.append(np.concatenate(np.array(y_samp), axis=0))        \n", "y_nde_treat = np.array(y_nde_treat)"]}, {"cell_type": "code", "execution_count": 42, "id": "f28e475d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1, figsize=(6,6))\n", "ax.plot([0, 1], [0, 1], ls=\"--\", color=\"k\")\n", "alpha, ecp = get_drp_coverage(np.swapaxes(y_nde_control, 0, 1), control_data[i_test_control,0][:,None], \n", "                              references=\"random\", metric=\"euclidean\")\n", "ax.plot(alpha, ecp, color='C0', label='control $q_C$') \n", "\n", "alpha, ecp = get_drp_coverage(np.swapaxes(y_nde_treat, 0, 1), treat_data[i_test_treat,0][:,None], \n", "                              references=\"random\", metric=\"euclidean\")\n", "ax.plot(alpha, ecp, color='C1', label='treated $q_T$')\n", "\n", "ax.legend(loc='lower right', handletextpad=0.1, fontsize=25)\n", "ax.set_ylabel(\"Expected Coverage\", fontsize=25)\n", "ax.set_ylim(0., 1.)\n", "ax.set_xlabel(\"Credibility Level\", fontsize=25)\n", "ax.set_xlim(0., 1.)\n", "fig.savefig('../figs/coverage_test.pdf', bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "id": "4087f2da", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sbi", "language": "python", "name": "sbi"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}