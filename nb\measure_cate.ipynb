{"cells": [{"cell_type": "markdown", "id": "58228285", "metadata": {}, "source": ["# Measure Conditional Average Treatment Effect for 27 Typologies"]}, {"cell_type": "code", "execution_count": 1, "id": "8c3d58b3", "metadata": {}, "outputs": [], "source": ["import os, glob\n", "import numpy as np\n", "import astropy.table as aTable\n", "from tqdm.notebook import tqdm, trange"]}, {"cell_type": "code", "execution_count": 2, "id": "daaf77e2", "metadata": {}, "outputs": [], "source": ["import torch\n", "from sbi import utils as Ut\n", "from sbi import inference as Inference\n", "from tensorboard.backend.event_processing.event_accumulator import EventAccumulator"]}, {"cell_type": "code", "execution_count": 3, "id": "1f9461dd", "metadata": {}, "outputs": [], "source": ["import corner as DFM\n", "# --- plotting ---\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "mpl.rcParams['text.usetex'] = True\n", "mpl.rcParams['font.family'] = 'serif'\n", "mpl.rcParams['axes.linewidth'] = 1.5\n", "mpl.rcParams['axes.xmargin'] = 1\n", "mpl.rcParams['xtick.labelsize'] = 'x-large'\n", "mpl.rcParams['xtick.major.size'] = 5\n", "mpl.rcParams['xtick.major.width'] = 1.5\n", "mpl.rcParams['ytick.labelsize'] = 'x-large'\n", "mpl.rcParams['ytick.major.size'] = 5\n", "mpl.rcParams['ytick.major.width'] = 1.5\n", "mpl.rcParams['legend.frameon'] = False"]}, {"cell_type": "code", "execution_count": 4, "id": "ac5b30ff", "metadata": {}, "outputs": [], "source": ["if torch.cuda.is_available(): device = 'cuda'\n", "else: device = 'cpu'"]}, {"cell_type": "markdown", "id": "46465082", "metadata": {}, "source": ["## load compiled data set\n", "See data section in paper"]}, {"cell_type": "code", "execution_count": 5, "id": "6dc890dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["74288 entries; 14729 unique zipcodes\n"]}], "source": ["dat_dir = '/scratch/gpfs/chhahn/noah/' # directory with data\n", "\n", "data = aTable.Table.read(os.path.join(dat_dir, 'zipcode.fema.fsf.acs.rainfall.v2.csv'), format='csv')\n", "print('%i entries; %i unique zipcodes' % (len(data), len(np.unique(data['reportedZipcode']))))"]}, {"cell_type": "code", "execution_count": 6, "id": "5b85b1d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["43644 entries in control; 11258 unique zipcodes\n", "30644 entries in control; 5040 unique zipcodes\n"]}], "source": ["control = (data['communityRatingSystemDiscount'] == 11.)\n", "print(f\"{np.sum(control)} entries in control; {len(np.unique(data['reportedZipcode'][control]))} unique zipcodes\")\n", "\n", "treat = (data['communityRatingSystemDiscount'] < 11.)\n", "print(f\"{np.sum(treat)} entries in control; {len(np.unique(data['reportedZipcode'][treat]))} unique zipcodes\")"]}, {"cell_type": "code", "execution_count": 7, "id": "552700c2", "metadata": {}, "outputs": [], "source": ["columns = ['amountPaidOnTotalClaim_per_policy', 'mean_rainfall', 'avg_risk_score_all', \n", "           'median_household_income', 'population', 'renter_fraction', 'educated_fraction', 'white_fraction'] "]}, {"cell_type": "code", "execution_count": 8, "id": "66dd7061", "metadata": {}, "outputs": [], "source": ["control_data = np.vstack([np.ma.getdata(data[col][control].data) for col in columns]).T\n", "control_data[:,0] = np.log10(control_data[:,0])\n", "control_data[:,3] = np.log10(control_data[:,3])\n", "control_data[:,4] = np.log10(control_data[:,4])\n", "\n", "treat_data = np.vstack([np.ma.getdata(data[col][treat].data) for col in columns]).T\n", "treat_data[:,0] = np.log10(treat_data[:,0])\n", "treat_data[:,3] = np.log10(treat_data[:,3])\n", "treat_data[:,4] = np.log10(treat_data[:,4])"]}, {"cell_type": "code", "execution_count": 9, "id": "4338e4f1", "metadata": {}, "outputs": [], "source": ["_control_data = control_data[:,1:].copy()\n", "_control_data[:,2] = 10**_control_data[:,2]\n", "_control_data[:,3] = 10**_control_data[:,3]\n", "\n", "_treat_data = treat_data[:,1:].copy()\n", "_treat_data[:,2] = 10**_treat_data[:,2]\n", "_treat_data[:,3] = 10**_treat_data[:,3]"]}, {"cell_type": "markdown", "id": "425060ba", "metadata": {}, "source": ["## load $\\mathcal{Q}^C_\\phi(X)$ and $\\mathcal{Q}^T_\\phi(X)$ for testing covariate support\n", "See Appendix B for details on $\\mathcal{Q}^C_\\phi(X)$ and $\\mathcal{Q}^T_\\phi(X)$"]}, {"cell_type": "code", "execution_count": 10, "id": "2bbdfa6d", "metadata": {}, "outputs": [], "source": ["support_control = torch.load('../dat/qphi_support.v2.log.control.pt', \n", "                             map_location=torch.device(device))\n", "support_treat = torch.load('../dat/qphi_support.v2.log.treat.pt', \n", "                           map_location=torch.device(device))"]}, {"cell_type": "code", "execution_count": 11, "id": "9f3187ce", "metadata": {}, "outputs": [], "source": ["def within_support(covar, thresh=-10): \n", "    logcond = covar.copy()\n", "    logcond[2] = np.log10(logcond[2])\n", "    logcond[3] = np.log10(logcond[3])\n", "    logprob_control = support_control.log_prob(\n", "        torch.tensor(logcond.astype(np.float32)).to(device)).detach().cpu()[0]\n", "    logprob_treat = support_treat.log_prob(torch.tensor(logcond.astype(np.float32)).to(device)).detach().cpu()[0]\n", "    #print(logprob_control, logprob_treat)\n", "    return [False, True][(logprob_control > thresh) & (logprob_treat > thresh)]"]}, {"cell_type": "markdown", "id": "e95eec1b", "metadata": {}, "source": ["## read $q_\\phi$"]}, {"cell_type": "code", "execution_count": null, "id": "abbe74ac", "metadata": {}, "outputs": [], "source": ["def read_qphi(nde_name):\n", "    qphis = []\n", "    for fqphi in glob.glob('../dat/nde/%s/*.pt' % nde_name): \n", "        qphi = torch.load(fqphi, map_location=device)\n", "        qphis.append(qphi)\n", "    return qphis"]}, {"cell_type": "code", "execution_count": 13, "id": "2da14c77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2313 models trained\n", "[680, 1365, 1087, 78, 7]\n", "2609 models trained\n", "[462, 1287, 1547, 2243, 269]\n"]}], "source": ["qphis_control = read_qphi('qphi_control')\n", "qphis_treat = read_qphi('qphi_treat')"]}, {"cell_type": "markdown", "id": "acd858bb", "metadata": {}, "source": ["# CATE for 27 typologies\n", "and save to file "]}, {"cell_type": "code", "execution_count": 14, "id": "7b4c5d2e", "metadata": {}, "outputs": [], "source": ["income_low, income_mid, income_high = 4e4, 6e4, 9e4      # roughly 16, 50 and 84 percentile\n", "population_low, population_mid, population_high = 2.5e3, 1.2e4, 3e4     # roughly 16, 50, 84 percentile\n", "diversity_low, diversity_mid, diversity_high = 0.6, 0.85, 0.95      # roughly 16, 50, 84 percentile"]}, {"cell_type": "code", "execution_count": 17, "id": "d1c45f40", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/sbi/lib/python3.7/site-packages/ipykernel_launcher.py:68: VisibleDeprecationWarning: Creating an ndarray from ragged nested sequences (which is a list-or-tuple of lists-or-tuples-or ndarrays with different lengths or shapes) is deprecated. If you meant to do this, you must specify 'dtype=object' when creating the ndarray.\n"]}], "source": ["_ranges = [(0., 500.), (0.5, 5.), (2.5e4, 1e5), (0, 4.5e4), (0., 0.8), (0., 0.6), (0.0, 1.)]\n", "n_sample = 40000 \n", "\n", "cv_cates, cates, treats, controls, sig_cates = [], [], [], [], []\n", "for ii_covar, i_covar in enumerate([0, 1, 4, 5]):    \n", "    for i_inc, inc in enumerate([income_low, income_mid, income_high]): \n", "        for i_pop, pop in enumerate([population_low, population_mid, population_high]): \n", "            for i_div, div in enumerate([diversity_low, diversity_mid, diversity_high]):             \n", "                near = ((np.abs(_control_data[:,2] - inc) < 5e3) &\n", "                        (np.abs(_control_data[:,3] - pop) < 5e3) & \n", "                        (np.abs(_control_data[:,-1] - div) < 0.05))\n", "            \n", "                fid = np.median(_control_data[near], axis=0)\n", "                fid[2] = inc\n", "                fid[3] = pop\n", "                fid[-1] = div\n", "                \n", "                xs_covar = np.linspace(_ranges[i_covar][0], _ranges[i_covar][1], 10)\n", "                                \n", "                cv_cate, cate, treat, control = [], [], [], []\n", "                sig_cate = []\n", "                for _covar in np.concatenate([xs_covar]): \n", "                    covars = fid.copy()\n", "                    covars[i_covar] = _covar\n", "\n", "                    # make sure it's within support\n", "                    if not within_support(covars): continue\n", "\n", "                    covars[2] = np.log10(covars[2])\n", "                    covars[3] = np.log10(covars[3])        \n", "\n", "                    treat_samp, control_samp = [], []\n", "                    for qphi_treat in qphis_treat: \n", "                        _samp = qphi_treat.sample((int(n_sample/len(qphis_treat)),),\n", "                                                   x=torch.tensor(covars, dtype=torch.float32).to(device), \n", "                                                   show_progress_bars=False)    \n", "                        treat_samp.append(_samp.detach().cpu().numpy())\n", "                    treat_samp = np.array(treat_samp).flatten()\n", "\n", "                    for qphi_control in qphis_control: \n", "                        _samp = qphi_control.sample((int(n_sample/len(qphis_treat)),), \n", "                                                    x=torch.tensor(covars, dtype=torch.float32).to(device), \n", "                                                    show_progress_bars=False)\n", "                        control_samp.append(_samp.detach().cpu().numpy())\n", "                    control_samp = np.array(control_samp).flatten()\n", "\n", "                    cv_cate.append(_covar)\n", "                    cate.append(np.mean(10**treat_samp) - np.mean(10**control_samp))\n", "                    treat.append(np.mean(10**treat_samp))\n", "                    control.append(np.mean(10**control_samp))          \n", "                    \n", "                    # uncertainty of CATE\n", "                    sig_treat = np.std(10**treat_samp)/np.sqrt(float(n_sample))\n", "                    sig_control = np.std(10**control_samp)/np.sqrt(float(n_sample))\n", "                    #print(np.sqrt(sig_treat**2 + sig_control**2))\n", "                    sig_cate.append(np.sqrt(sig_treat**2 + sig_control**2))\n", "                    \n", "                cv_cates.append(cv_cate)\n", "                cates.append(cate)\n", "                treats.append(treat)\n", "                controls.append(control)  \n", "                sig_cates.append(sig_cate)\n", "                \n", "np.save('../dat/typology_covar.npy', np.array(cv_cates))\n", "np.save('../dat/typology_cate.npy', np.array(cates))\n", "np.save('../dat/typology_y_treat.npy', np.array(treats))\n", "np.save('../dat/typology_y_control.npy', np.array(controls))\n", "np.save('../dat/typology_sig_cate.npy', np.array(sig_cates))"]}], "metadata": {"kernelspec": {"display_name": "sbi", "language": "python", "name": "sbi"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}