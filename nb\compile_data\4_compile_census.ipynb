{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# we only use ACS census data from 2008 - 2020"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import astropy.table as aTable"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["127535 entries\n"]}], "source": ["fema = aTable.Table.read('/Users/<USER>/data/noah/fema.zipcode.fsf.acs.rainfall.csv', format='csv')\n", "print('%i entries' % len(fema))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=5</i>\n", "<table id=\"table140247079988112\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th><th>zipcode</th><th>count_property</th><th>count_fema_sfha</th><th>pct_fema_sfha</th><th>count_fs_risk_2020_5</th><th>pct_fs_risk_2020_5</th><th>count_fs_risk_2050_5</th><th>pct_fs_risk_2050_5</th><th>count_fs_risk_2020_100</th><th>pct_fs_risk_2020_100</th><th>count_fs_risk_2050_100</th><th>pct_fs_risk_2050_100</th><th>count_fs_risk_2020_500</th><th>pct_fs_risk_2020_500</th><th>count_fs_risk_2050_500</th><th>pct_fs_risk_2050_500</th><th>pct_fs_fema_difference_2020</th><th>avg_risk_score_all</th><th>avg_risk_score_2_10</th><th>avg_risk_fsf_2020_100</th><th>avg_risk_fsf_2020_500</th><th>avg_risk_score_sfha</th><th>avg_risk_score_no_sfha</th><th>count_floodfactor1</th><th>count_floodfactor2</th><th>count_floodfactor3</th><th>count_floodfactor4</th><th>count_floodfactor5</th><th>count_floodfactor6</th><th>count_floodfactor7</th><th>count_floodfactor8</th><th>count_floodfactor9</th><th>count_floodfactor10</th><th>2018.population</th><th>2018.median_household_income</th><th>2018.renter_fraction</th><th>2018.educated_fraction</th><th>2014.population</th><th>2014.median_household_income</th><th>2014.renter_fraction</th><th>2014.educated_fraction</th><th>2010.population</th><th>2010.median_household_income</th><th>2010.renter_fraction</th><th>2010.educated_fraction</th><th>AREA</th><th>mean_rainfall</th><th>sum_rainfall</th><th>median_rainfall</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th></tr></thead>\n", "<tr><td>1001</td><td>2005</td><td>10</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>0.6666666666666666</td><td>3.0</td><td>5958.55</td><td>0.0</td><td>455800</td><td>26400</td><td>5958.55</td><td>482200</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td><td>0.005208333333341666</td><td>321.2156677246094</td><td>963.6470031738281</td><td>312.2799987792969</td></tr>\n", "<tr><td>1001</td><td>2011</td><td>4</td><td>MA</td><td>-72.6</td><td>42.0</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>10545.77</td><td>1473.46</td><td>250000</td><td>100000</td><td>12019.23</td><td>350000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td><td>0.005208333333341666</td><td>147.1486612955729</td><td>441.44598388671875</td><td>147.62399291992188</td></tr>\n", "<tr><td>1001</td><td>2011</td><td>8</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>1.0</td><td>2.0</td><td>9320.56</td><td>0.0</td><td>262000</td><td>5000</td><td>9320.56</td><td>267000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td><td>0.005208333333341666</td><td>266.2306620279948</td><td>798.6919860839844</td><td>266.02899169921875</td></tr>\n", "<tr><td>1001</td><td>2014</td><td>8</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>590.26</td><td>0.0</td><td>250000</td><td>100000</td><td>590.26</td><td>350000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td><td>0.005208333333341666</td><td>99.75433349609375</td><td>299.26300048828125</td><td>101.04000091552734</td></tr>\n", "<tr><td>1002</td><td>1991</td><td>8</td><td>MA</td><td>-70.6</td><td>41.7</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>33851.0</td><td>0.0</td><td>70000</td><td>0</td><td>33851.0</td><td>70000</td><td>1002</td><td>7048</td><td>80</td><td>1.1</td><td>194</td><td>2.8</td><td>204</td><td>2.9</td><td>497</td><td>7.1</td><td>513</td><td>7.3</td><td>654</td><td>9.3</td><td>670</td><td>9.5</td><td>5.9</td><td>1.53</td><td>6.6</td><td>7.61</td><td>6.7</td><td>5.9</td><td>1.48</td><td>6378</td><td>15</td><td>53</td><td>87</td><td>34</td><td>172</td><td>94</td><td>14</td><td>98</td><td>103</td><td>30099</td><td>60540</td><td>0.4102129638858434</td><td>0.7107991817732948</td><td>29970</td><td>48923</td><td>0.4468134801468135</td><td>0.65777009247425</td><td>28718</td><td>56175</td><td>0.3966153631868515</td><td>0.6823411319589221</td><td>0.01388888888891111</td><td>233.21237564086914</td><td>1865.6990051269531</td><td>232.2135009765625</td></tr>\n", "</table>"], "text/plain": ["<Table length=5>\n", "reportedZipcode yearOfLoss monthOfLoss ...    sum_rainfall     median_rainfall  \n", "     int64        int64       int64    ...      float64            float64      \n", "--------------- ---------- ----------- ... ------------------ ------------------\n", "           1001       2005          10 ...  963.6470031738281  312.2799987792969\n", "           1001       2011           4 ... 441.44598388671875 147.62399291992188\n", "           1001       2011           8 ...  798.6919860839844 266.02899169921875\n", "           1001       2014           8 ... 299.26300048828125 101.04000091552734\n", "           1002       1991           8 ... 1865.6990051269531  232.2135009765625"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["fema[:5]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["year2018 = (fema['yearOfLoss'] > 2016)\n", "year2014 = (fema['yearOfLoss'] > 2012) & (fema['yearOfLoss'] <= 2016)\n", "year2010 = (fema['yearOfLoss'] > 2003) & (fema['yearOfLoss'] <= 2012)\n", "years = year2010 | year2014 | year2018"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["74288 of 127535 entries have year > 2003\n"]}], "source": ["print('%i of %i entries have year > 2003' % (np.sum(years), len(years)))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["census_cols = []\n", "for col in fema.columns: \n", "    if '2014' in col: \n", "        census_cols.append(col.split('.')[1])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["for col in census_cols: \n", "    fema[col] = 0.\n", "    fema[col][year2018] = fema['2018.'+col][year2018]\n", "    fema[col][year2014] = fema['2018.'+col][year2014]\n", "    fema[col][year2010] = fema['2018.'+col][year2010]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["fema_post2003 = fema[years]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=10</i>\n", "<table id=\"table140248135668816\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th><th>zipcode</th><th>count_property</th><th>count_fema_sfha</th><th>pct_fema_sfha</th><th>count_fs_risk_2020_5</th><th>pct_fs_risk_2020_5</th><th>count_fs_risk_2050_5</th><th>pct_fs_risk_2050_5</th><th>count_fs_risk_2020_100</th><th>pct_fs_risk_2020_100</th><th>count_fs_risk_2050_100</th><th>pct_fs_risk_2050_100</th><th>count_fs_risk_2020_500</th><th>pct_fs_risk_2020_500</th><th>count_fs_risk_2050_500</th><th>pct_fs_risk_2050_500</th><th>pct_fs_fema_difference_2020</th><th>avg_risk_score_all</th><th>avg_risk_score_2_10</th><th>avg_risk_fsf_2020_100</th><th>avg_risk_fsf_2020_500</th><th>avg_risk_score_sfha</th><th>avg_risk_score_no_sfha</th><th>count_floodfactor1</th><th>count_floodfactor2</th><th>count_floodfactor3</th><th>count_floodfactor4</th><th>count_floodfactor5</th><th>count_floodfactor6</th><th>count_floodfactor7</th><th>count_floodfactor8</th><th>count_floodfactor9</th><th>count_floodfactor10</th><th>2018.population</th><th>2018.median_household_income</th><th>2018.renter_fraction</th><th>2018.educated_fraction</th><th>2014.population</th><th>2014.median_household_income</th><th>2014.renter_fraction</th><th>2014.educated_fraction</th><th>2010.population</th><th>2010.median_household_income</th><th>2010.renter_fraction</th><th>2010.educated_fraction</th><th>AREA</th><th>mean_rainfall</th><th>sum_rainfall</th><th>median_rainfall</th><th>population</th><th>median_household_income</th><th>renter_fraction</th><th>educated_fraction</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th></tr></thead>\n", "<tr><td>1001</td><td>2005</td><td>10</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>0.6666666666666666</td><td>3.0</td><td>5958.55</td><td>0.0</td><td>455800</td><td>26400</td><td>5958.55</td><td>482200</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td><td>0.005208333333341666</td><td>321.2156677246094</td><td>963.6470031738281</td><td>312.2799987792969</td><td>16064.0</td><td>66088.0</td><td>0.21358316733067728</td><td>0.3477663785443089</td></tr>\n", "<tr><td>2301</td><td>2018</td><td>3</td><td>MA</td><td>-71.025</td><td>42.1</td><td>0.0</td><td>11.0</td><td>0.75</td><td>4.0</td><td>15672.57</td><td>0.0</td><td>950000</td><td>280000</td><td>15672.57</td><td>1230000</td><td>2301</td><td>14504</td><td>500</td><td>3.4</td><td>19</td><td>0.1</td><td>21</td><td>0.1</td><td>1067</td><td>7.4</td><td>1139</td><td>7.9</td><td>1717</td><td>11.8</td><td>1783</td><td>12.3</td><td>3.9</td><td>1.52</td><td>5.25</td><td>6.25</td><td>5.37</td><td>4.72</td><td>1.41</td><td>12721</td><td>66</td><td>183</td><td>372</td><td>111</td><td>813</td><td>195</td><td>10</td><td>21</td><td>12</td><td>63920</td><td>57977</td><td>0.4450250312891114</td><td>0.18880007943206076</td><td>60659</td><td>44873</td><td>0.49417234046060765</td><td>0.16710068325383526</td><td>60265</td><td>45017</td><td>0.4729279017671949</td><td>0.1732250891979937</td><td>0.0017361111111138887</td><td>192.7689971923828</td><td>192.7689971923828</td><td>192.7689971923828</td><td>63920.0</td><td>57977.0</td><td>0.4450250312891114</td><td>0.18880007943206076</td></tr>\n", "<tr><td>3223</td><td>2011</td><td>8</td><td>NH</td><td>-71.70000000000002</td><td>43.89230769230768</td><td>0.0</td><td>11.0</td><td>0.15384615384615385</td><td>13.0</td><td>179782.44999999995</td><td>13200.69</td><td>472700</td><td>40000</td><td>192983.13999999996</td><td>512700</td><td>3223</td><td>2889</td><td>191</td><td>6.6</td><td>227</td><td>7.9</td><td>239</td><td>8.3</td><td>403</td><td>13.9</td><td>419</td><td>14.5</td><td>484</td><td>16.8</td><td>502</td><td>17.4</td><td>7.3</td><td>2.07</td><td>7.19</td><td>8.0</td><td>7.37</td><td>6.17</td><td>1.79</td><td>2387</td><td>17</td><td>24</td><td>31</td><td>38</td><td>102</td><td>66</td><td>13</td><td>101</td><td>110</td><td>3382</td><td>72059</td><td>0.12921348314606743</td><td>0.46470828233374134</td><td>3417</td><td>61150</td><td>0.09891717881182324</td><td>0.428997390980246</td><td>3389</td><td>47176</td><td>0.14753614635585718</td><td>0.28383381324557794</td><td>0.026041666666708332</td><td>289.333534749349</td><td>4340.003021240234</td><td>287.7439880371094</td><td>3382.0</td><td>72059.0</td><td>0.12921348314606743</td><td>0.46470828233374134</td></tr>\n", "<tr><td>5851</td><td>2011</td><td>8</td><td>VT</td><td>-72.02105263157895</td><td>44.5</td><td>0.0</td><td>11.0</td><td>0.42105263157894735</td><td>19.0</td><td>498325.39</td><td>84901.78000000001</td><td>2130400</td><td>450200</td><td>583227.17</td><td>2580600</td><td>5851</td><td>2745</td><td>99</td><td>3.6</td><td>237</td><td>8.6</td><td>247</td><td>9.0</td><td>666</td><td>24.3</td><td>675</td><td>24.6</td><td>761</td><td>27.7</td><td>771</td><td>28.1</td><td>20.7</td><td>2.7</td><td>7.04</td><td>7.51</td><td>7.1</td><td>6.96</td><td>2.54</td><td>1974</td><td>10</td><td>28</td><td>39</td><td>26</td><td>349</td><td>49</td><td>8</td><td>87</td><td>175</td><td>6212</td><td>56160</td><td>0.25386349001931746</td><td>0.27491840321365807</td><td>6635</td><td>40629</td><td>0.2690278824415976</td><td>0.241271370093908</td><td>6505</td><td>37857</td><td>0.33512682551883166</td><td>0.24353607485840925</td><td>0.022569444444480554</td><td>235.97354008601263</td><td>3067.656021118164</td><td>233.77999877929688</td><td>6212.0</td><td>56160.0</td><td>0.25386349001931746</td><td>0.27491840321365807</td></tr>\n", "<tr><td>6831</td><td>2015</td><td>5</td><td>CT</td><td>-73.7</td><td>41.0</td><td>0.0</td><td>11.0</td><td>0.0</td><td>12.0</td><td>15997.09</td><td>0.0</td><td>3000000</td><td>0</td><td>15997.09</td><td>3000000</td><td>6831</td><td>5311</td><td>387</td><td>7.3</td><td>220</td><td>4.1</td><td>231</td><td>4.3</td><td>801</td><td>15.1</td><td>835</td><td>15.7</td><td>1003</td><td>18.9</td><td>1021</td><td>19.2</td><td>7.8</td><td>2.06</td><td>6.49</td><td>7.22</td><td>6.59</td><td>5.34</td><td>1.8</td><td>4289</td><td>20</td><td>64</td><td>129</td><td>53</td><td>322</td><td>145</td><td>30</td><td>153</td><td>106</td><td>15240</td><td>205360</td><td>0.25406824146981627</td><td>0.6533272561531449</td><td>14574</td><td>140820</td><td>0.1929463428022506</td><td>0.6497212510630256</td><td>14526</td><td>131447</td><td>0.18635550048189453</td><td>0.5826779205831575</td><td>0.008680555555569444</td><td>39.41079940795898</td><td>197.05399703979492</td><td>39.92900085449219</td><td>15240.0</td><td>205360.0</td><td>0.25406824146981627</td><td>0.6533272561531449</td></tr>\n", "<tr><td>7666</td><td>2005</td><td>4</td><td>NJ</td><td>-74.0</td><td>40.9</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>32.05</td><td>290.29</td><td>28700</td><td>17100</td><td>322.34000000000003</td><td>45800</td><td>7666</td><td>12247</td><td>137</td><td>1.1</td><td>3</td><td>0.0</td><td>2</td><td>0.0</td><td>289</td><td>2.4</td><td>298</td><td>2.4</td><td>399</td><td>3.3</td><td>410</td><td>3.3</td><td>1.2</td><td>1.15</td><td>5.57</td><td>6.32</td><td>5.66</td><td>3.14</td><td>1.13</td><td>11837</td><td>11</td><td>41</td><td>61</td><td>29</td><td>161</td><td>88</td><td>6</td><td>12</td><td>1</td><td>40461</td><td>113934</td><td>0.17268480759249646</td><td>0.5424547891418432</td><td>40668</td><td>101128</td><td>0.21060784892298612</td><td>0.557979420018709</td><td>39781</td><td>94964</td><td>0.18008597068952514</td><td>0.559539636115015</td><td>0.0017361111111138887</td><td>125.3270034790039</td><td>125.3270034790039</td><td>125.3270034790039</td><td>40461.0</td><td>113934.0</td><td>0.17268480759249646</td><td>0.5424547891418432</td></tr>\n", "<tr><td>8102</td><td>2004</td><td>8</td><td>NJ</td><td>-75.1</td><td>39.95</td><td>0.0</td><td>11.0</td><td>1.0</td><td>2.0</td><td>6797.33</td><td>0.0</td><td>100900</td><td>0</td><td>6797.33</td><td>100900</td><td>8102</td><td>2892</td><td>204</td><td>7.1</td><td>5</td><td>0.2</td><td>11</td><td>0.4</td><td>108</td><td>3.7</td><td>183</td><td>6.3</td><td>474</td><td>16.4</td><td>519</td><td>17.9</td><td>-3.3</td><td>1.54</td><td>3.99</td><td>6.46</td><td>4.19</td><td>5.1</td><td>1.27</td><td>2373</td><td>67</td><td>206</td><td>77</td><td>59</td><td>78</td><td>21</td><td>3</td><td>5</td><td>3</td><td>8050</td><td>22212</td><td>0.6283229813664596</td><td>0.09858247422680412</td><td>7601</td><td>20165</td><td>0.7125378239705302</td><td>0.05069238377843719</td><td>7380</td><td>17772</td><td>0.6005420054200542</td><td>0.0784557907845579</td><td>0.0017361111111138887</td><td>175.98699951171875</td><td>175.98699951171875</td><td>175.98699951171875</td><td>8050.0</td><td>22212.0</td><td>0.6283229813664596</td><td>0.09858247422680412</td></tr>\n", "<tr><td>8873</td><td>2010</td><td>3</td><td>NJ</td><td>-74.5</td><td>40.516666666666666</td><td>0.8333333333333334</td><td>6.833333333333333</td><td>0.8333333333333334</td><td>6.0</td><td>42108.729999999996</td><td>42688.07</td><td>710000</td><td>559600</td><td>84796.79999999999</td><td>1269600</td><td>8873</td><td>22756</td><td>185</td><td>0.8</td><td>39</td><td>0.2</td><td>49</td><td>0.2</td><td>368</td><td>1.6</td><td>413</td><td>1.8</td><td>505</td><td>2.2</td><td>535</td><td>2.4</td><td>0.8</td><td>1.12</td><td>5.93</td><td>6.94</td><td>6.18</td><td>4.99</td><td>1.08</td><td>22220</td><td>31</td><td>50</td><td>43</td><td>56</td><td>166</td><td>95</td><td>20</td><td>55</td><td>20</td><td>54416</td><td>92063</td><td>0.2939025286680388</td><td>0.5306373491197047</td><td>53094</td><td>87213</td><td>0.28253663314122124</td><td>0.4742384158000891</td><td>50554</td><td>84108</td><td>0.2674170194247735</td><td>0.4572650752035325</td><td>0.008680555555569444</td><td>277.87080078125</td><td>1389.35400390625</td><td>281.4419860839844</td><td>54416.0</td><td>92063.0</td><td>0.2939025286680388</td><td>0.5306373491197047</td></tr>\n", "<tr><td>11357</td><td>2004</td><td>8</td><td>NY</td><td>-73.8</td><td>40.8</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>707.94</td><td>0.0</td><td>250000</td><td>60000</td><td>707.94</td><td>310000</td><td>11357</td><td>9731</td><td>193</td><td>2.0</td><td>96</td><td>1.0</td><td>107</td><td>1.1</td><td>805</td><td>8.3</td><td>881</td><td>9.1</td><td>1414</td><td>14.5</td><td>1566</td><td>16.1</td><td>6.3</td><td>1.66</td><td>5.11</td><td>6.44</td><td>5.37</td><td>5.48</td><td>1.58</td><td>8165</td><td>38</td><td>290</td><td>329</td><td>112</td><td>664</td><td>34</td><td>1</td><td>28</td><td>70</td><td>40118</td><td>82858</td><td>0.24948900742808713</td><td>0.3580485851575013</td><td>39259</td><td>74255</td><td>0.2377544002649074</td><td>0.3647982912460812</td><td>40705</td><td>76014</td><td>0.26780493796830857</td><td>0.34216148394316526</td><td>0.0017361111111138887</td><td>111.26300048828125</td><td>111.26300048828125</td><td>111.26300048828125</td><td>40118.0</td><td>82858.0</td><td>0.24948900742808713</td><td>0.3580485851575013</td></tr>\n", "<tr><td>11963</td><td>2008</td><td>3</td><td>NY</td><td>-72.3</td><td>41.0</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>2756.38</td><td>0.0</td><td>250000</td><td>100000</td><td>2756.38</td><td>350000</td><td>11963</td><td>6571</td><td>639</td><td>9.7</td><td>249</td><td>3.8</td><td>459</td><td>7.0</td><td>721</td><td>11.0</td><td>898</td><td>13.7</td><td>913</td><td>13.9</td><td>1544</td><td>23.5</td><td>1.2</td><td>2.04</td><td>5.43</td><td>7.61</td><td>7.05</td><td>6.56</td><td>1.55</td><td>5026</td><td>64</td><td>462</td><td>148</td><td>153</td><td>238</td><td>143</td><td>29</td><td>228</td><td>80</td><td>6966</td><td>103929</td><td>0.1929371231696813</td><td>0.5561572402727637</td><td>6710</td><td>86310</td><td>0.19254843517138598</td><td>0.5578721745908028</td><td>6737</td><td>81875</td><td>0.20127653258126763</td><td>0.5615810578575521</td><td>0.0034722222222277775</td><td>114.5099983215332</td><td>229.0199966430664</td><td>114.5099983215332</td><td>6966.0</td><td>103929.0</td><td>0.1929371231696813</td><td>0.5561572402727637</td></tr>\n", "</table>"], "text/plain": ["<Table length=10>\n", "reportedZipcode yearOfLoss ...   renter_fraction    educated_fraction \n", "     int64        int64    ...       float64             float64      \n", "--------------- ---------- ... ------------------- -------------------\n", "           1001       2005 ... 0.21358316733067728  0.3477663785443089\n", "           2301       2018 ...  0.4450250312891114 0.18880007943206076\n", "           3223       2011 ... 0.12921348314606743 0.46470828233374134\n", "           5851       2011 ... 0.25386349001931746 0.27491840321365807\n", "           6831       2015 ... 0.25406824146981627  0.6533272561531449\n", "           7666       2005 ... 0.17268480759249646  0.5424547891418432\n", "           8102       2004 ...  0.6283229813664596 0.09858247422680412\n", "           8873       2010 ...  0.2939025286680388  0.5306373491197047\n", "          11357       2004 ... 0.24948900742808713  0.3580485851575013\n", "          11963       2008 ...  0.1929371231696813  0.5561572402727637"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["fema_post2003[::1000][:10]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 of 74288 have no income or number\n"]}], "source": ["no_income_or_number = (fema_post2003['population'] == 0) | (fema_post2003['median_household_income'] == 0)\n", "print('%i of %i have no income or number' % (np.sum(no_income_or_number), len(fema_post2003)))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["fema_post2003['amountPaidOnTotalClaim_per_policy'] = fema_post2003['amountPaidOnTotalClaim'] / fema_post2003['policyCount']"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["fema_post2003.write('/Users/<USER>/data/noah/zipcode.fema.fsf.acs.rainfall.csv', format='csv', overwrite=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "noah", "language": "python", "name": "noah"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}