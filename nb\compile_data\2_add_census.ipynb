{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# add ACS census information to FEMA zipcode catalog"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import astropy.table as aTable"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# --- plotting ---\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "mpl.rcParams['text.usetex'] = True\n", "mpl.rcParams['font.family'] = 'serif'\n", "mpl.rcParams['axes.linewidth'] = 1.5\n", "mpl.rcParams['axes.xmargin'] = 1\n", "mpl.rcParams['xtick.labelsize'] = 'x-large'\n", "mpl.rcParams['xtick.major.size'] = 5\n", "mpl.rcParams['xtick.major.width'] = 1.5\n", "mpl.rcParams['ytick.labelsize'] = 'x-large'\n", "mpl.rcParams['ytick.major.size'] = 5\n", "mpl.rcParams['ytick.major.width'] = 1.5\n", "mpl.rcParams['legend.frameon'] = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## read FEMA data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["198492\n"]}], "source": ["fema = aTable.Table.read('/Users/<USER>/data/noah/fema.zipcode.fsf.csv', format='csv')\n", "print(len(fema))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=4</i>\n", "<table id=\"table140425044692304\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th><th>zipcode</th><th>count_property</th><th>count_fema_sfha</th><th>pct_fema_sfha</th><th>count_fs_risk_2020_5</th><th>pct_fs_risk_2020_5</th><th>count_fs_risk_2050_5</th><th>pct_fs_risk_2050_5</th><th>count_fs_risk_2020_100</th><th>pct_fs_risk_2020_100</th><th>count_fs_risk_2050_100</th><th>pct_fs_risk_2050_100</th><th>count_fs_risk_2020_500</th><th>pct_fs_risk_2020_500</th><th>count_fs_risk_2050_500</th><th>pct_fs_risk_2050_500</th><th>pct_fs_fema_difference_2020</th><th>avg_risk_score_all</th><th>avg_risk_score_2_10</th><th>avg_risk_fsf_2020_100</th><th>avg_risk_fsf_2020_500</th><th>avg_risk_score_sfha</th><th>avg_risk_score_no_sfha</th><th>count_floodfactor1</th><th>count_floodfactor2</th><th>count_floodfactor3</th><th>count_floodfactor4</th><th>count_floodfactor5</th><th>count_floodfactor6</th><th>count_floodfactor7</th><th>count_floodfactor8</th><th>count_floodfactor9</th><th>count_floodfactor10</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th></tr></thead>\n", "<tr><td>1001</td><td>2014</td><td>8</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>590.26</td><td>0.0</td><td>250000</td><td>100000</td><td>590.26</td><td>350000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td></tr>\n", "<tr><td>1001</td><td>2011</td><td>8</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>1.0</td><td>2.0</td><td>9320.56</td><td>0.0</td><td>262000</td><td>5000</td><td>9320.56</td><td>267000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td></tr>\n", "<tr><td>1001</td><td>2011</td><td>4</td><td>MA</td><td>-72.6</td><td>42.0</td><td>0.0</td><td>11.0</td><td>1.0</td><td>1.0</td><td>10545.77</td><td>1473.46</td><td>250000</td><td>100000</td><td>12019.23</td><td>350000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td></tr>\n", "<tr><td>1001</td><td>1987</td><td>3</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>1185.9</td><td>0.0</td><td>50000</td><td>18000</td><td>1185.9</td><td>68000</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td></tr>\n", "</table>"], "text/plain": ["<Table length=4>\n", "reportedZipcode yearOfLoss ... count_floodfactor9 count_floodfactor10\n", "     int64        int64    ...       int64               int64       \n", "--------------- ---------- ... ------------------ -------------------\n", "           1001       2014 ...                144                 540\n", "           1001       2011 ...                144                 540\n", "           1001       2011 ...                144                 540\n", "           1001       1987 ...                144                 540"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["fema[1:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## read in ACS census data: 2016 - 2020"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["census2018 = aTable.Table.read('/Users/<USER>/data/noah/ACS2016_20new.csv', format='csv')[1:]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["income = census2018['Median Household Income (In 2020 Inflation Adjusted Dollars)']\n", "population = census2018['Total Population']\n", "population_density = census2018['Population Density (Per Sq. Mile)']"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:3: RuntimeWarning: invalid value encountered in true_divide\n", "  This is separate from the ipykernel package so we can avoid doing imports until\n"]}], "source": ["renter_fraction = (\n", "    np.array(census2018['Total Population in Occupied Housing Units: Renter Occupied'].data).astype(float) / \n", "    np.array(census2018['Total Population'].data).astype(float))\n", "renter_fraction = aTable.MaskedColumn(renter_fraction, dtype=float)\n", "renter_fraction.mask = np.array(census2018['Total Population'].data).astype(float) == 0."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:3: RuntimeWarning: invalid value encountered in true_divide\n", "  This is separate from the ipykernel package so we can avoid doing imports until\n"]}], "source": ["educated_fraction = (\n", "    np.array(census2018['Population 25 Years and Over: Bachelor\\'s Degree or More'].data).astype(float) / \n", "    np.array(census2018['Population 25 Years and Over:'].data).astype(float))\n", "\n", "educated_fraction = aTable.MaskedColumn(educated_fraction, dtype=float)\n", "educated_fraction.mask = np.array(census2018['Population 25 Years and Over:'].data).astype(float) == 0."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["acs2018 = aTable.Table()\n", "acs2018['zipcode'] = np.array([int(name.split(' ')[1]) for name in census2018['Qualifying Name']])\n", "acs2018['2018.population'] = population\n", "acs2018['2018.median_household_income'] = income\n", "acs2018['2018.renter_fraction'] = renter_fraction\n", "acs2018['2018.educated_fraction'] = educated_fraction"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["comb_mask = (acs2018['2018.median_household_income'].mask | acs2018['2018.renter_fraction'].mask | acs2018['2018.educated_fraction'].mask)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2557 of 33120 entries do not have income data\n", "7.72 percent\n"]}], "source": ["print('%i of %i entries do not have income data' % (np.sum(comb_mask), len(acs2018)))\n", "print('%.2f percent' % (np.sum(income.mask)/len(income)*100.))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["acs2018 = acs2018[~comb_mask]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## read in ACS census data: 2012 - 2016"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["census2014 = aTable.Table.read('/Users/<USER>/data/noah/ACS2012_16socexplnew.csv', format='csv')[1:]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=5</i>\n", "<table id=\"table140425699119248\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>FIPS</th><th>Geographic Identifier</th><th>Name of Area</th><th>Qualifying Name</th><th>State/U.S.-Abbreviation (USPS)</th><th>Summary Level</th><th>Geographic Component</th><th>File Identification</th><th>Logical Record Number</th><th>US</th><th>Region</th><th>Division</th><th>State (Census Code)</th><th>State (FIPS)</th><th>County</th><th>County Subdivision (FIPS)</th><th>Place (FIPS Code)</th><th>Place (State FIPS + Place FIPS)</th><th>Census Tract</th><th>Block Group</th><th>Consolidated City</th><th>American Indian Area/Alaska Native Area/Hawaiian Home Land (Census)</th><th>American Indian Area/Alaska Native Area/Hawaiian Home Land (FIPS)</th><th>American Indian Trust Land/Hawaiian Home Land Indicator</th><th>American Indian Tribal Subdivision (Census)</th><th>American Indian Tribal Subdivision (FIPS)</th><th>Alaska Native Regional Corporation (FIPS)</th><th>Metropolitan and Micropolitan Statistical Area</th><th>Combined Statistical Area</th><th>Metropolitan Division</th><th>Metropolitan Area Central City</th><th>Metropolitan/Micropolitan Indicator Flag</th><th>New England City and Town Combined Statistical Area</th><th>New England City and Town Area</th><th>New England City and Town Area Division</th><th>Urban Area</th><th>Urban Area Central Place</th><th>Current Congressional District ***</th><th>State Legislative District Upper</th><th>State Legislative District Lower</th><th>Voting District</th><th>ZIP Code Tabulation Area (3-digit)</th><th>ZIP Code Tabulation Area (5-digit)</th><th>Subbarrio (FIPS)</th><th>School District (Elementary)</th><th>School District (Secondary)</th><th>School District (Unified)</th><th>Urban/Rural</th><th>Principal City Indicator</th><th>Traffic Analysis Zone</th><th>Urban Growth Area</th><th>Tribal Tract</th><th>Tribal Block Group</th><th>Public Use Microdata Area - 5% File</th><th>Public Use Microdata Area - 1% File</th><th>Total Population</th><th>Total Population_1</th><th>Population Density (Per Sq. Mile)</th><th>Area (Land)</th><th>Total Population:</th><th>Total Population: White Alone</th><th>Total Population: Black or African American Alone</th><th>Total Population: American Indian and Alaska Native Alone</th><th>Total Population: Asian Alone</th><th>Total Population: Native Hawaiian and Other Pacific Islander Alone</th><th>Total Population: Some Other Race Alone</th><th>Total Population: Two or More Races</th><th>Population 25 Years and Over:</th><th>Population 25 Years and Over: Less than High School</th><th>Population 25 Years and Over: High School Graduate or More (Includes Equivalency)</th><th>Population 25 Years and Over: Some College or More</th><th>Population 25 Years and Over: Bachelor&apos;s Degree or More</th><th>Population 25 Years and Over: Master&apos;s Degree or More</th><th>Population 25 Years and Over: Professional School Degree or More</th><th>Population 25 Years and Over: Doctorate Degree</th><th>Renter-Occupied Housing Units</th><th>Renter-Occupied Housing Units: Less than High School Graduate</th><th>Renter-Occupied Housing Units: High School Graduate (Including Equivalency)</th><th>Renter-Occupied Housing Units: Some College or Associate&apos;s Degree</th><th>Renter-Occupied Housing Units: Bachelor&apos;s Degree or Higher</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars)</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars):</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): White Alone Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Black or African American Alone Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): American Indian and Alaska Native Alone  Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Asian Alone</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Native Hawaiian and Other Pacific Islander Alone  Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Some Other Race Alone Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Two or More Races Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): Hispanic or Latino Householder</th><th>Median Household Income (In 2016 Inflation Adjusted Dollars): White Alone Householder, Not Hispanic or Latino</th><th>Households:</th><th>Households: with Earnings</th><th>Households: No Earnings</th><th>Per Capita Income (In 2016 Inflation Adjusted Dollars)</th><th>Lowest Quintile</th><th>Second Quintile</th><th>Third Quintile</th><th>Fourth Quintile</th><th>Lower Limit of Top 5 Percent</th><th>Occupied Housing Units:</th><th>Occupied Housing Units: Owner Occupied</th><th>Occupied Housing Units: Renter Occupied</th><th>Housing Units:</th><th>Housing Units: Occupied</th><th>Housing Units: Vacant</th><th>Vacant Housing Units:</th><th>Vacant Housing Units: for Rent</th><th>Vacant Housing Units: for Sale Only</th><th>Vacant Housing Units: Other Vacant</th><th>Total Population in Occupied Housing Units: Renter Occupied</th><th>Total Population in Occupied Housing Units: Renter Occupied: 1, Detached or Attached</th><th>Total Population in Occupied Housing Units: Renter Occupied: 2 to 4</th><th>Total Population in Occupied Housing Units: Renter Occupied: 5 or More</th><th>Total Population in Occupied Housing Units: Renter Occupied: Mobile Home</th><th>Total Population in Occupied Housing Units: Renter Occupied: Boat, Rv, Van, Etc.</th><th>Population for Whom Poverty Status Is Determined:</th><th>Population for Whom Poverty Status Is Determined: Under .50</th><th>Population for Whom Poverty Status Is Determined: .50 to .74</th><th>Population for Whom Poverty Status Is Determined: .75 to .99</th><th>Population for Whom Poverty Status Is Determined: 1.00 to 1.49</th><th>Population for Whom Poverty Status Is Determined: 1.50 to 1.99</th><th>Population for Whom Poverty Status Is Determined: 2.00 and Over</th></tr></thead>\n", "<thead><tr><th>str8</th><th>str12</th><th>str11</th><th>str11</th><th>str10</th><th>str10</th><th>str11</th><th>str10</th><th>str12</th><th>str6</th><th>str10</th><th>str12</th><th>str11</th><th>str9</th><th>str10</th><th>str10</th><th>str9</th><th>str11</th><th>str9</th><th>str10</th><th>str10</th><th>str10</th><th>str12</th><th>str11</th><th>str10</th><th>str8</th><th>str8</th><th>str8</th><th>str7</th><th>str10</th><th>str8</th><th>str8</th><th>str9</th><th>str10</th><th>str12</th><th>str6</th><th>str8</th><th>str10</th><th>str8</th><th>str8</th><th>str7</th><th>str9</th><th>str9</th><th>str10</th><th>str9</th><th>str9</th><th>str9</th><th>str6</th><th>str7</th><th>str7</th><th>str7</th><th>str8</th><th>str8</th><th>str9</th><th>str9</th><th>str13</th><th>str13</th><th>str13</th><th>str19</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th></tr></thead>\n", "<tr><td>00600601</td><td>86000US00601</td><td>00601 ZCTA5</td><td>00601 ZCTA5</td><td>pr</td><td>860</td><td>00</td><td>ACSSF</td><td>0007371</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>006</td><td>00601</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>17800</td><td>17800</td><td>276.6221</td><td>64.3477081747097</td><td>17800</td><td>14436</td><td>108</td><td>21</td><td>0</td><td>0</td><td>3046</td><td>189</td><td>11887</td><td>4387</td><td>7500</td><td>4515</td><td>2221</td><td>376</td><td>98</td><td>71</td><td>2802</td><td>1046</td><td>715</td><td>636</td><td>405</td><td>11507</td><td>11507</td><td>10727</td><td>--</td><td>--</td><td>--</td><td>--</td><td>15144</td><td>2885</td><td>11533</td><td>--</td><td>5959</td><td>2790</td><td>3169</td><td>7229</td><td>2499</td><td>8545</td><td>15568</td><td>31780</td><td>59765</td><td>5959</td><td>3157</td><td>2802</td><td>7290</td><td>5959</td><td>1331</td><td>1331</td><td>68</td><td>44</td><td>1219</td><td>8028</td><td>6609</td><td>593</td><td>751</td><td>75</td><td>0</td><td>17754</td><td>7477</td><td>2089</td><td>1512</td><td>2365</td><td>1712</td><td>2599</td></tr>\n", "<tr><td>00900920</td><td>86000US00920</td><td>00920 ZCTA5</td><td>00920 ZCTA5</td><td>pr</td><td>860</td><td>00</td><td>ACSSF</td><td>0007471</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>009</td><td>00920</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>17576</td><td>17576</td><td>6209.149</td><td>2.83066176368385</td><td>17576</td><td>12024</td><td>3713</td><td>6</td><td>31</td><td>0</td><td>1187</td><td>615</td><td>12880</td><td>2331</td><td>10549</td><td>7968</td><td>4216</td><td>1216</td><td>408</td><td>119</td><td>3108</td><td>550</td><td>636</td><td>1078</td><td>844</td><td>25591</td><td>25591</td><td>25813</td><td>21818</td><td>--</td><td>--</td><td>--</td><td>29625</td><td>28264</td><td>25665</td><td>29722</td><td>7131</td><td>4517</td><td>2614</td><td>14263</td><td>10219</td><td>19381</td><td>31038</td><td>46764</td><td>88732</td><td>7131</td><td>4023</td><td>3108</td><td>9332</td><td>7131</td><td>2201</td><td>2201</td><td>505</td><td>254</td><td>1442</td><td>7642</td><td>4855</td><td>1558</td><td>1132</td><td>97</td><td>0</td><td>17518</td><td>2842</td><td>1606</td><td>1348</td><td>3160</td><td>3182</td><td>5380</td></tr>\n", "<tr><td>01101109</td><td>86000US01109</td><td>01109 ZCTA5</td><td>01109 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010750</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>011</td><td>01109</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>30139</td><td>30139</td><td>5538.129</td><td>5.44209046528401</td><td>30139</td><td>12657</td><td>11455</td><td>67</td><td>241</td><td>0</td><td>4146</td><td>1573</td><td>16075</td><td>3495</td><td>12580</td><td>6925</td><td>2203</td><td>782</td><td>227</td><td>49</td><td>5115</td><td>1267</td><td>1660</td><td>1740</td><td>448</td><td>35076</td><td>35076</td><td>32779</td><td>38864</td><td>--</td><td>54563</td><td>--</td><td>25717</td><td>35952</td><td>25419</td><td>39419</td><td>9571</td><td>6436</td><td>3135</td><td>14977</td><td>12339</td><td>25414</td><td>41336</td><td>66017</td><td>118920</td><td>9571</td><td>4456</td><td>5115</td><td>10836</td><td>9571</td><td>1265</td><td>1265</td><td>234</td><td>66</td><td>965</td><td>14557</td><td>4289</td><td>7955</td><td>2313</td><td>0</td><td>0</td><td>27087</td><td>3678</td><td>2517</td><td>1952</td><td>4457</td><td>3118</td><td>11365</td></tr>\n", "<tr><td>01501519</td><td>86000US01519</td><td>01519 ZCTA5</td><td>01519 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010850</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>015</td><td>01519</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>6548</td><td>6548</td><td>600.0903</td><td>10.9116907105361</td><td>6548</td><td>5791</td><td>375</td><td>0</td><td>177</td><td>0</td><td>2</td><td>203</td><td>4703</td><td>168</td><td>4535</td><td>3665</td><td>2445</td><td>893</td><td>258</td><td>97</td><td>495</td><td>6</td><td>144</td><td>123</td><td>222</td><td>114615</td><td>114615</td><td>114135</td><td>130729</td><td>--</td><td>118750</td><td>--</td><td>--</td><td>--</td><td>--</td><td>113317</td><td>2376</td><td>2028</td><td>348</td><td>47905</td><td>46128</td><td>93176</td><td>134738</td><td>198400</td><td>250001</td><td>2376</td><td>1881</td><td>495</td><td>2422</td><td>2376</td><td>46</td><td>46</td><td>0</td><td>0</td><td>46</td><td>1011</td><td>473</td><td>70</td><td>468</td><td>0</td><td>0</td><td>6548</td><td>163</td><td>84</td><td>26</td><td>239</td><td>151</td><td>5885</td></tr>\n", "<tr><td>01801879</td><td>86000US01879</td><td>01879 ZCTA5</td><td>01879 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010950</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>018</td><td>01879</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>12133</td><td>12133</td><td>722.9926</td><td>16.7816391427296</td><td>12133</td><td>10775</td><td>103</td><td>6</td><td>989</td><td>0</td><td>24</td><td>236</td><td>8677</td><td>444</td><td>8233</td><td>6042</td><td>3776</td><td>1219</td><td>226</td><td>102</td><td>597</td><td>73</td><td>100</td><td>212</td><td>212</td><td>99325</td><td>99325</td><td>103750</td><td>--</td><td>--</td><td>79247</td><td>--</td><td>--</td><td>--</td><td>--</td><td>104306</td><td>4332</td><td>3760</td><td>572</td><td>44664</td><td>51079</td><td>79900</td><td>119982</td><td>178695</td><td>250001</td><td>4332</td><td>3735</td><td>597</td><td>4446</td><td>4332</td><td>114</td><td>114</td><td>0</td><td>0</td><td>114</td><td>1461</td><td>778</td><td>119</td><td>564</td><td>0</td><td>0</td><td>12067</td><td>524</td><td>217</td><td>166</td><td>397</td><td>294</td><td>10469</td></tr>\n", "</table>"], "text/plain": ["<Table length=5>\n", "  FIPS   ... Population for Whom Poverty Status Is Determined: 2.00 and Over\n", "  str8   ...                              str13                             \n", "-------- ... ---------------------------------------------------------------\n", "00600601 ...                                                            2599\n", "00900920 ...                                                            5380\n", "01101109 ...                                                           11365\n", "01501519 ...                                                            5885\n", "01801879 ...                                                           10469"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["census2014[::100][:5]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:7: RuntimeWarning: invalid value encountered in true_divide\n", "  import sys\n", "/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:13: RuntimeWarning: invalid value encountered in true_divide\n", "  del sys.path[0]\n"]}], "source": ["income = census2014['Median Household Income (In 2016 Inflation Adjusted Dollars)']\n", "population = census2014['Total Population']\n", "population_density = census2014['Population Density (Per Sq. Mile)']\n", "\n", "renter_fraction = (\n", "    np.array(census2014['Total Population in Occupied Housing Units: Renter Occupied'].data).astype(float) / \n", "    np.array(census2014['Total Population'].data).astype(float))\n", "renter_fraction = aTable.MaskedColumn(renter_fraction, dtype=float)\n", "renter_fraction.mask = np.array(census2014['Total Population'].data).astype(float) == 0.\n", "\n", "educated_fraction = (\n", "    np.array(census2014['Population 25 Years and Over: Bachelor\\'s Degree or More'].data).astype(float) / \n", "    np.array(census2014['Population 25 Years and Over:'].data).astype(float))\n", "\n", "educated_fraction = aTable.MaskedColumn(educated_fraction, dtype=float)\n", "educated_fraction.mask = np.array(census2014['Population 25 Years and Over:'].data).astype(float) == 0."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["acs2014 = aTable.Table()\n", "acs2014['zipcode'] = np.array([int(name.split(' ')[0]) for name in census2014['Qualifying Name']])\n", "acs2014['2014.population'] = population\n", "acs2014['2014.median_household_income'] = income\n", "acs2014['2014.renter_fraction'] = renter_fraction\n", "acs2014['2014.educated_fraction'] = educated_fraction"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["comb_mask = (acs2014['2014.median_household_income'].mask | \n", "             acs2014['2014.renter_fraction'].mask | \n", "             acs2014['2014.educated_fraction'].mask)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2008 of 33120 entries do not have income data\n", "6.06 percent\n"]}], "source": ["print('%i of %i entries do not have income data' % (np.sum(comb_mask), len(acs2014)))\n", "print('%.2f percent' % (np.sum(income.mask)/len(income)*100.))\n", "\n", "acs2014 = acs2014[~comb_mask]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## read ACS census data: 2008 - 2012"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["census2010 = aTable.Table.read('/Users/<USER>/data/noah/ACS2008_12socialexplorer.csv', format='csv')[1:]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=5</i>\n", "<table id=\"table140424553739920\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>FIPS</th><th>Geographic Identifier</th><th>Name of Area</th><th>Qualifying Name</th><th>State/U.S.-Abbreviation (USPS)</th><th>Summary Level</th><th>Geographic Component</th><th>File Identification</th><th>Logical Record Number</th><th>US</th><th>Region</th><th>Division</th><th>State (Census Code)</th><th>State (FIPS)</th><th>County</th><th>County Subdivision (FIPS)</th><th>Place (FIPS Code)</th><th>Place (State FIPS + Place FIPS)</th><th>Census Tract</th><th>Block Group</th><th>Consolidated City</th><th>American Indian Area/Alaska Native Area/Hawaiian Home Land (Census)</th><th>American Indian Area/Alaska Native Area/Hawaiian Home Land (FIPS)</th><th>American Indian Trust Land/Hawaiian Home Land Indicator</th><th>American Indian Tribal Subdivision (Census)</th><th>American Indian Tribal Subdivision (FIPS)</th><th>Alaska Native Regional Corporation (FIPS)</th><th>Metropolitan and Micropolitan Statistical Area</th><th>Combined Statistical Area</th><th>Metropolitan Division</th><th>Metropolitan Area Central City</th><th>Metropolitan/Micropolitan Indicator Flag</th><th>New England City and Town Combined Statistical Area</th><th>New England City and Town Area</th><th>New England City and Town Area Division</th><th>Urban Area</th><th>Urban Area Central Place</th><th>Current Congressional District ***</th><th>State Legislative District Upper</th><th>State Legislative District Lower</th><th>Voting District</th><th>ZIP Code Tabulation Area (3-digit)</th><th>ZIP Code Tabulation Area (5-digit)</th><th>Subbarrio (FIPS)</th><th>School District (Elementary)</th><th>School District (Secondary)</th><th>School District (Unified)</th><th>Urban/Rural</th><th>Principal City Indicator</th><th>Traffic Analysis Zone</th><th>Urban Growth Area</th><th>Tribal Tract</th><th>Tribal Block Group</th><th>Public Use Microdata Area - 5% File</th><th>Public Use Microdata Area - 1% File</th><th>Total Population</th><th>Total Population_1</th><th>Population Density (Per Sq. Mile)</th><th>Area (Land)</th><th>Total Population:</th><th>Total Population: White Alone</th><th>Total Population: Black or African American Alone</th><th>Total Population: American Indian and Alaska Native Alone</th><th>Total Population: Asian Alone</th><th>Total Population: Native Hawaiian and Other Pacific Islander Alone</th><th>Total Population: Some Other Race Alone</th><th>Total Population: Two or More Races</th><th>Population 25 Years and Over:</th><th>Population 25 Years and Over: Less than High School</th><th>Population 25 Years and Over: High School Graduate or More (Includes Equivalency)</th><th>Population 25 Years and Over: Some College or More</th><th>Population 25 Years and Over: Bachelor&apos;s Degree or More</th><th>Population 25 Years and Over: Master&apos;s Degree or More</th><th>Population 25 Years and Over: Professional School Degree or More</th><th>Population 25 Years and Over: Doctorate Degree</th><th>Renter-Occupied Housing Units</th><th>Renter-Occupied Housing Units: Less than High School Graduate</th><th>Renter-Occupied Housing Units: High School Graduate (Including Equivalency)</th><th>Renter-Occupied Housing Units: Some College or Associate&apos;s Degree</th><th>Renter-Occupied Housing Units: Bachelor&apos;s Degree or Higher</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars)</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars):</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): White Alone Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Black or African American Alone Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): American Indian and Alaska Native Alone  Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Asian Alone</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Native Hawaiian and Other Pacific Islander Alone  Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Some Other Race Alone Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Two or More Races Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): Hispanic or Latino Householder</th><th>Median Household Income (In 2012 Inflation Adjusted Dollars): White Alone Householder, Not Hispanic or Latino</th><th>Households:</th><th>Households: with Earnings</th><th>Households: No Earnings</th><th>Per Capita Income (In 2012 Inflation Adjusted Dollars)</th><th>Lowest Quintile</th><th>Second Quintile</th><th>Third Quintile</th><th>Fourth Quintile</th><th>Lower Limit of Top 5 Percent</th><th>Occupied Housing Units:</th><th>Occupied Housing Units: Owner Occupied</th><th>Occupied Housing Units: Renter Occupied</th><th>Housing Units:</th><th>Housing Units: Occupied</th><th>Housing Units: Vacant</th><th>Vacant Housing Units:</th><th>Vacant Housing Units: for Rent</th><th>Vacant Housing Units: for Sale Only</th><th>Vacant Housing Units: Other Vacant</th><th>Total Population in Occupied Housing Units: Renter Occupied</th><th>Total Population in Occupied Housing Units: Renter Occupied: 1, Detached or Attached</th><th>Total Population in Occupied Housing Units: Renter Occupied: 2 to 4</th><th>Total Population in Occupied Housing Units: Renter Occupied: 5 or More</th><th>Total Population in Occupied Housing Units: Renter Occupied: Mobile Home</th><th>Total Population in Occupied Housing Units: Renter Occupied: Boat, Rv, Van, Etc.</th><th>Population for Whom Poverty Status Is Determined:</th><th>Population for Whom Poverty Status Is Determined: Under .50</th><th>Population for Whom Poverty Status Is Determined: .50 to .74</th><th>Population for Whom Poverty Status Is Determined: .75 to .99</th><th>Population for Whom Poverty Status Is Determined: 1.00 to 1.49</th><th>Population for Whom Poverty Status Is Determined: 1.50 to 1.99</th><th>Population for Whom Poverty Status Is Determined: 2.00 and Over</th></tr></thead>\n", "<thead><tr><th>str8</th><th>str12</th><th>str11</th><th>str11</th><th>str10</th><th>str10</th><th>str11</th><th>str10</th><th>str12</th><th>str6</th><th>str10</th><th>str12</th><th>str11</th><th>str9</th><th>str10</th><th>str10</th><th>str9</th><th>str11</th><th>str9</th><th>str10</th><th>str10</th><th>str10</th><th>str12</th><th>str11</th><th>str10</th><th>str8</th><th>str8</th><th>str8</th><th>str7</th><th>str10</th><th>str8</th><th>str8</th><th>str9</th><th>str10</th><th>str12</th><th>str6</th><th>str8</th><th>str10</th><th>str8</th><th>str8</th><th>str7</th><th>str9</th><th>str9</th><th>str10</th><th>str9</th><th>str9</th><th>str9</th><th>str6</th><th>str7</th><th>str7</th><th>str7</th><th>str8</th><th>str8</th><th>str9</th><th>str9</th><th>str13</th><th>str13</th><th>str13</th><th>str19</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str14</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th><th>str13</th></tr></thead>\n", "<tr><td>00600601</td><td>86000US00601</td><td>00601 ZCTA5</td><td>00601 ZCTA5</td><td>pr</td><td>860</td><td>00</td><td>ACSSF</td><td>0009814</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>006</td><td>00601</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>18544</td><td>18544</td><td>288.1843</td><td>64.3477081747097</td><td>18544</td><td>17259</td><td>192</td><td>5</td><td>0</td><td>0</td><td>1021</td><td>67</td><td>11896</td><td>4731</td><td>7165</td><td>4149</td><td>1867</td><td>272</td><td>93</td><td>23</td><td>1952</td><td>903</td><td>553</td><td>289</td><td>207</td><td>13495</td><td>13495</td><td>13534</td><td>32500</td><td>--</td><td>--</td><td>--</td><td>9207</td><td>2499</td><td>13681</td><td>3702</td><td>5477</td><td>2896</td><td>2581</td><td>7283</td><td>3261</td><td>9064</td><td>18746</td><td>35050</td><td>66572</td><td>5477</td><td>3525</td><td>1952</td><td>6762</td><td>5477</td><td>1285</td><td>1285</td><td>53</td><td>141</td><td>1091</td><td>6128</td><td>5058</td><td>346</td><td>724</td><td>0</td><td>0</td><td>18375</td><td>6672</td><td>1808</td><td>1720</td><td>3034</td><td>1554</td><td>3587</td></tr>\n", "<tr><td>00900920</td><td>86000US00920</td><td>00920 ZCTA5</td><td>00920 ZCTA5</td><td>pr</td><td>860</td><td>00</td><td>ACSSF</td><td>0009914</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>009</td><td>00920</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>19528</td><td>19528</td><td>6898.74</td><td>2.83066176368385</td><td>19528</td><td>12541</td><td>3953</td><td>283</td><td>23</td><td>0</td><td>765</td><td>1963</td><td>14171</td><td>3655</td><td>10516</td><td>7424</td><td>4229</td><td>1174</td><td>462</td><td>108</td><td>2915</td><td>858</td><td>607</td><td>755</td><td>695</td><td>25191</td><td>25191</td><td>25813</td><td>22454</td><td>15000</td><td>--</td><td>--</td><td>28382</td><td>24474</td><td>25133</td><td>70972</td><td>7443</td><td>4814</td><td>2629</td><td>15098</td><td>9842</td><td>18217</td><td>32753</td><td>53410</td><td>98066</td><td>7443</td><td>4528</td><td>2915</td><td>8912</td><td>7443</td><td>1469</td><td>1469</td><td>363</td><td>186</td><td>920</td><td>7538</td><td>4896</td><td>1867</td><td>775</td><td>0</td><td>0</td><td>19492</td><td>2953</td><td>1885</td><td>1634</td><td>3287</td><td>2936</td><td>6797</td></tr>\n", "<tr><td>01101109</td><td>86000US01109</td><td>01109 ZCTA5</td><td>01109 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010472</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>011</td><td>01109</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>31503</td><td>31503</td><td>5788.768</td><td>5.44209046528401</td><td>31503</td><td>9570</td><td>13042</td><td>134</td><td>401</td><td>4</td><td>6516</td><td>1836</td><td>16336</td><td>4060</td><td>12276</td><td>6366</td><td>2067</td><td>643</td><td>155</td><td>40</td><td>4918</td><td>1624</td><td>1483</td><td>1434</td><td>377</td><td>32174</td><td>32174</td><td>37226</td><td>34909</td><td>--</td><td>55208</td><td>--</td><td>19547</td><td>32904</td><td>21473</td><td>39323</td><td>9553</td><td>6655</td><td>2898</td><td>13796</td><td>11142</td><td>23363</td><td>41302</td><td>67603</td><td>102981</td><td>9553</td><td>4635</td><td>4918</td><td>10907</td><td>9553</td><td>1354</td><td>1354</td><td>304</td><td>85</td><td>965</td><td>14448</td><td>3522</td><td>8053</td><td>2859</td><td>14</td><td>0</td><td>28243</td><td>4913</td><td>2032</td><td>2510</td><td>3770</td><td>3473</td><td>11545</td></tr>\n", "<tr><td>01501519</td><td>86000US01519</td><td>01519 ZCTA5</td><td>01519 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010572</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>015</td><td>01519</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>6386</td><td>6386</td><td>585.2438</td><td>10.9116907105361</td><td>6386</td><td>6082</td><td>10</td><td>0</td><td>220</td><td>0</td><td>0</td><td>74</td><td>4127</td><td>151</td><td>3976</td><td>3410</td><td>2546</td><td>887</td><td>289</td><td>139</td><td>482</td><td>39</td><td>110</td><td>127</td><td>206</td><td>109050</td><td>109050</td><td>107031</td><td>--</td><td>--</td><td>128854</td><td>--</td><td>--</td><td>92708</td><td>--</td><td>106563</td><td>2293</td><td>2037</td><td>256</td><td>45969</td><td>46320</td><td>84471</td><td>132026</td><td>182205</td><td>250001</td><td>2293</td><td>1811</td><td>482</td><td>2310</td><td>2293</td><td>17</td><td>17</td><td>0</td><td>0</td><td>17</td><td>988</td><td>231</td><td>274</td><td>483</td><td>0</td><td>0</td><td>6313</td><td>154</td><td>116</td><td>105</td><td>237</td><td>104</td><td>5597</td></tr>\n", "<tr><td>01801879</td><td>86000US01879</td><td>01879 ZCTA5</td><td>01879 ZCTA5</td><td>us</td><td>860</td><td>00</td><td>ACSSF</td><td>0010672</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>018</td><td>01879</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>--</td><td>11484</td><td>11484</td><td>684.3193</td><td>16.7816391427296</td><td>11484</td><td>10668</td><td>75</td><td>0</td><td>536</td><td>0</td><td>63</td><td>142</td><td>7756</td><td>443</td><td>7313</td><td>5041</td><td>3200</td><td>1187</td><td>136</td><td>41</td><td>592</td><td>71</td><td>220</td><td>116</td><td>185</td><td>104888</td><td>104888</td><td>107759</td><td>51719</td><td>--</td><td>70625</td><td>--</td><td>--</td><td>9143</td><td>9679</td><td>107759</td><td>4055</td><td>3375</td><td>680</td><td>41862</td><td>41033</td><td>77403</td><td>122165</td><td>177778</td><td>250001</td><td>4055</td><td>3463</td><td>592</td><td>4192</td><td>4055</td><td>137</td><td>137</td><td>0</td><td>0</td><td>137</td><td>1221</td><td>585</td><td>242</td><td>394</td><td>0</td><td>0</td><td>11457</td><td>134</td><td>261</td><td>177</td><td>334</td><td>379</td><td>10172</td></tr>\n", "</table>"], "text/plain": ["<Table length=5>\n", "  FIPS   ... Population for Whom Poverty Status Is Determined: 2.00 and Over\n", "  str8   ...                              str13                             \n", "-------- ... ---------------------------------------------------------------\n", "00600601 ...                                                            3587\n", "00900920 ...                                                            6797\n", "01101109 ...                                                           11545\n", "01501519 ...                                                            5597\n", "01801879 ...                                                           10172"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["census2010[::100][:5]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:7: RuntimeWarning: invalid value encountered in true_divide\n", "  import sys\n", "/opt/anaconda3/envs/noah/lib/python3.7/site-packages/ipykernel_launcher.py:13: RuntimeWarning: invalid value encountered in true_divide\n", "  del sys.path[0]\n"]}], "source": ["income = census2010['Median Household Income (In 2012 Inflation Adjusted Dollars)']\n", "population = census2010['Total Population']\n", "population_density = census2010['Population Density (Per Sq. Mile)']\n", "\n", "renter_fraction = (\n", "    np.array(census2010['Total Population in Occupied Housing Units: Renter Occupied'].data).astype(float) / \n", "    np.array(census2010['Total Population'].data).astype(float))\n", "renter_fraction = aTable.MaskedColumn(renter_fraction, dtype=float)\n", "renter_fraction.mask = np.array(census2010['Total Population'].data).astype(float) == 0.\n", "\n", "educated_fraction = (\n", "    np.array(census2010['Population 25 Years and Over: Bachelor\\'s Degree or More'].data).astype(float) / \n", "    np.array(census2010['Population 25 Years and Over:'].data).astype(float))\n", "\n", "educated_fraction = aTable.MaskedColumn(educated_fraction, dtype=float)\n", "educated_fraction.mask = np.array(census2010['Population 25 Years and Over:'].data).astype(float) == 0."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["acs2010 = aTable.Table()\n", "acs2010['zipcode'] = np.array([int(name.split(' ')[0]) for name in census2010['Qualifying Name']])\n", "acs2010['2010.population'] = population\n", "acs2010['2010.median_household_income'] = income\n", "acs2010['2010.renter_fraction'] = renter_fraction\n", "acs2010['2010.educated_fraction'] = educated_fraction"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["comb_mask = (acs2010['2010.median_household_income'].mask | \n", "             acs2010['2010.renter_fraction'].mask | \n", "             acs2010['2010.educated_fraction'].mask)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["872 of 33120 entries do not have income data\n", "2.62 percent\n"]}], "source": ["print('%i of %i entries do not have income data' % (np.sum(comb_mask), len(acs2010)))\n", "print('%.2f percent' % (np.sum(income.mask)/len(income)*100.))\n", "\n", "acs2010 = acs2010[~comb_mask]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## join all the tables"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["comb = aTable.join(fema, acs2018, keys='zipcode', join_type='left')\n", "comb = aTable.join(comb, acs2014, keys='zipcode', join_type='left')\n", "comb = aTable.join(comb, acs2010, keys='zipcode', join_type='left')"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<i>Table length=10</i>\n", "<table id=\"table140425055081360\" class=\"table-striped table-bordered table-condensed\">\n", "<thead><tr><th>reportedZipcode</th><th>yearOfLoss</th><th>monthOfLoss</th><th>state</th><th>longitude</th><th>latitude</th><th>crs_treat</th><th>communityRatingSystemDiscount</th><th>primaryResidence</th><th>policyCount</th><th>amountPaidOnBuildingClaim</th><th>amountPaidOnContentsClaim</th><th>totalBuildingInsuranceCoverage</th><th>totalContentsInsuranceCoverage</th><th>amountPaidOnTotalClaim</th><th>totalTotalInsuranceCoverage</th><th>zipcode</th><th>count_property</th><th>count_fema_sfha</th><th>pct_fema_sfha</th><th>count_fs_risk_2020_5</th><th>pct_fs_risk_2020_5</th><th>count_fs_risk_2050_5</th><th>pct_fs_risk_2050_5</th><th>count_fs_risk_2020_100</th><th>pct_fs_risk_2020_100</th><th>count_fs_risk_2050_100</th><th>pct_fs_risk_2050_100</th><th>count_fs_risk_2020_500</th><th>pct_fs_risk_2020_500</th><th>count_fs_risk_2050_500</th><th>pct_fs_risk_2050_500</th><th>pct_fs_fema_difference_2020</th><th>avg_risk_score_all</th><th>avg_risk_score_2_10</th><th>avg_risk_fsf_2020_100</th><th>avg_risk_fsf_2020_500</th><th>avg_risk_score_sfha</th><th>avg_risk_score_no_sfha</th><th>count_floodfactor1</th><th>count_floodfactor2</th><th>count_floodfactor3</th><th>count_floodfactor4</th><th>count_floodfactor5</th><th>count_floodfactor6</th><th>count_floodfactor7</th><th>count_floodfactor8</th><th>count_floodfactor9</th><th>count_floodfactor10</th><th>2018.population</th><th>2018.median_household_income</th><th>2018.renter_fraction</th><th>2018.educated_fraction</th><th>2014.population</th><th>2014.median_household_income</th><th>2014.renter_fraction</th><th>2014.educated_fraction</th><th>2010.population</th><th>2010.median_household_income</th><th>2010.renter_fraction</th><th>2010.educated_fraction</th></tr></thead>\n", "<thead><tr><th>int64</th><th>int64</th><th>int64</th><th>str2</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>int64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>float64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>int64</th><th>str13</th><th>str13</th><th>float64</th><th>float64</th><th>str13</th><th>str13</th><th>float64</th><th>float64</th><th>str13</th><th>str13</th><th>float64</th><th>float64</th></tr></thead>\n", "<tr><td>1001</td><td>2005</td><td>10</td><td>MA</td><td>-72.6</td><td>42.1</td><td>0.0</td><td>11.0</td><td>0.6666666666666666</td><td>3.0</td><td>5958.55</td><td>0.0</td><td>455800</td><td>26400</td><td>5958.55</td><td>482200</td><td>1001</td><td>5343</td><td>499</td><td>9.3</td><td>574</td><td>10.7</td><td>673</td><td>12.6</td><td>1208</td><td>22.6</td><td>1228</td><td>23.0</td><td>1478</td><td>27.7</td><td>1530</td><td>28.6</td><td>13.3</td><td>2.82</td><td>7.34</td><td>8.34</td><td>7.51</td><td>9.65</td><td>2.11</td><td>3813</td><td>52</td><td>104</td><td>122</td><td>40</td><td>279</td><td>234</td><td>15</td><td>144</td><td>540</td><td>16064</td><td>66088</td><td>0.21358316733067728</td><td>0.3477663785443089</td><td>17423</td><td>56714</td><td>0.21735636801928485</td><td>0.27717140661029976</td><td>17380</td><td>63682</td><td>0.18601841196777905</td><td>0.24933451641526175</td></tr>\n", "<tr><td>1845</td><td>1998</td><td>6</td><td>MA</td><td>-71.1</td><td>42.6</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>1792.11</td><td>470.4</td><td>100000</td><td>25000</td><td>2262.5099999999998</td><td>125000</td><td>1845</td><td>8276</td><td>342</td><td>4.1</td><td>232</td><td>2.8</td><td>247</td><td>3.0</td><td>762</td><td>9.2</td><td>794</td><td>9.6</td><td>988</td><td>11.9</td><td>1018</td><td>12.3</td><td>5.1</td><td>1.7</td><td>6.64</td><td>7.62</td><td>6.79</td><td>5.2</td><td>1.54</td><td>7255</td><td>34</td><td>58</td><td>121</td><td>43</td><td>279</td><td>180</td><td>28</td><td>96</td><td>182</td><td>31262</td><td>113916</td><td>0.23910818245793616</td><td>0.6124180105538295</td><td>29705</td><td>102008</td><td>0.20488133310890422</td><td>0.5964559001208216</td><td>28114</td><td>97044</td><td>0.1708757202817102</td><td>0.5528379997821113</td></tr>\n", "<tr><td>2045</td><td>2015</td><td>1</td><td>MA</td><td>-70.86666666666666</td><td>42.300000000000004</td><td>1.0</td><td>7.0</td><td>0.5</td><td>124.0</td><td>140853.49000000002</td><td>1111.3700000000001</td><td>30341600</td><td>172300</td><td>141964.86000000002</td><td>30513900</td><td>2045</td><td>4747</td><td>2983</td><td>62.8</td><td>1168</td><td>24.6</td><td>2307</td><td>48.6</td><td>2978</td><td>62.7</td><td>3170</td><td>66.8</td><td>3199</td><td>67.4</td><td>3381</td><td>71.2</td><td>-0.1</td><td>5.76</td><td>7.67</td><td>8.18</td><td>7.96</td><td>7.71</td><td>2.46</td><td>1357</td><td>83</td><td>96</td><td>54</td><td>161</td><td>674</td><td>429</td><td>97</td><td>1265</td><td>531</td><td>10478</td><td>105403</td><td>0.18572246611948845</td><td>0.5017194355508123</td><td>10437</td><td>78114</td><td>0.27354603813356326</td><td>0.44029495718363465</td><td>10342</td><td>73396</td><td>0.2137884355057049</td><td>0.378358301349511</td></tr>\n", "<tr><td>2190</td><td>1996</td><td>10</td><td>MA</td><td>-70.95</td><td>42.2</td><td>0.0</td><td>11.0</td><td>0.0</td><td>2.0</td><td>7211.06</td><td>1352.6</td><td>132400</td><td>18000</td><td>8563.66</td><td>150400</td><td>2190</td><td>5395</td><td>116</td><td>2.2</td><td>2</td><td>0.0</td><td>3</td><td>0.1</td><td>348</td><td>6.5</td><td>388</td><td>7.2</td><td>514</td><td>9.5</td><td>533</td><td>9.9</td><td>4.3</td><td>1.45</td><td>5.52</td><td>6.37</td><td>5.65</td><td>4.72</td><td>1.37</td><td>4862</td><td>19</td><td>42</td><td>79</td><td>39</td><td>247</td><td>85</td><td>6</td><td>16</td><td>0</td><td>17466</td><td>87424</td><td>0.2780258788503378</td><td>0.42634528287403084</td><td>16886</td><td>80139</td><td>0.2547080421651072</td><td>0.3547734627831715</td><td>16176</td><td>78532</td><td>0.20839515331355093</td><td>0.34660805103008363</td></tr>\n", "<tr><td>2664</td><td>1991</td><td>8</td><td>MA</td><td>-70.20666666666668</td><td>41.620000000000005</td><td>0.0</td><td>11.0</td><td>0.26666666666666666</td><td>15.0</td><td>166251.79000000004</td><td>40587.77</td><td>1758900</td><td>266200</td><td>206839.56000000003</td><td>2025100</td><td>2664</td><td>6453</td><td>1416</td><td>21.9</td><td>98</td><td>1.5</td><td>585</td><td>9.1</td><td>866</td><td>13.4</td><td>1122</td><td>17.4</td><td>1319</td><td>20.4</td><td>2184</td><td>33.8</td><td>-8.5</td><td>2.15</td><td>4.41</td><td>6.85</td><td>5.97</td><td>4.15</td><td>1.59</td><td>4269</td><td>759</td><td>224</td><td>223</td><td>154</td><td>433</td><td>172</td><td>32</td><td>154</td><td>33</td><td>8565</td><td>66283</td><td>0.25125510799766493</td><td>0.2903908564978129</td><td>9049</td><td>52696</td><td>0.23251187976571996</td><td>0.3104282259211837</td><td>9562</td><td>49698</td><td>0.2678309976992261</td><td>0.28992532424996725</td></tr>\n", "<tr><td>2893</td><td>1979</td><td>1</td><td>RI</td><td>-71.5</td><td>41.7</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>4247.2</td><td>0.0</td><td>58100</td><td>0</td><td>4247.2</td><td>58100</td><td>2893</td><td>9907</td><td>369</td><td>3.7</td><td>337</td><td>3.4</td><td>359</td><td>3.6</td><td>1147</td><td>11.6</td><td>1217</td><td>12.3</td><td>1468</td><td>14.8</td><td>1534</td><td>15.5</td><td>7.9</td><td>1.84</td><td>6.45</td><td>7.38</td><td>6.62</td><td>7.55</td><td>1.62</td><td>8372</td><td>68</td><td>107</td><td>146</td><td>90</td><td>509</td><td>175</td><td>41</td><td>191</td><td>208</td><td>29283</td><td>60813</td><td>0.4248198613530034</td><td>0.2459999077788537</td><td>29450</td><td>51332</td><td>0.4299830220713073</td><td>0.2248867069486405</td><td>29487</td><td>51809</td><td>0.43344524705802556</td><td>0.21516705947594622</td></tr>\n", "<tr><td>3842</td><td>1990</td><td>12</td><td>NH</td><td>-70.8</td><td>42.89999999999999</td><td>0.0</td><td>11.0</td><td>0.0</td><td>30.0</td><td>18017.84</td><td>2627.1</td><td>323500</td><td>73600</td><td>20644.94</td><td>397100</td><td>3842</td><td>6355</td><td>1714</td><td>27.0</td><td>1406</td><td>22.1</td><td>1700</td><td>26.8</td><td>2203</td><td>34.7</td><td>2454</td><td>38.6</td><td>2594</td><td>40.8</td><td>2919</td><td>45.9</td><td>7.7</td><td>3.84</td><td>7.19</td><td>8.43</td><td>7.77</td><td>8.58</td><td>2.09</td><td>3436</td><td>246</td><td>148</td><td>156</td><td>213</td><td>448</td><td>205</td><td>28</td><td>727</td><td>748</td><td>15787</td><td>81639</td><td>0.2028884525242288</td><td>0.4464446444644464</td><td>15156</td><td>77201</td><td>0.23489047242016364</td><td>0.4885721734596333</td><td>14983</td><td>67402</td><td>0.25795902022291933</td><td>0.3966735223106501</td></tr>\n", "<tr><td>4747</td><td>1987</td><td>4</td><td>ME</td><td>-68.3</td><td>46.0</td><td>0.0</td><td>11.0</td><td>0.0</td><td>1.0</td><td>130.45</td><td>0.0</td><td>14500</td><td>8800</td><td>130.45</td><td>23300</td><td>4747</td><td>87</td><td>4</td><td>4.6</td><td>12</td><td>13.8</td><td>12</td><td>13.8</td><td>28</td><td>32.2</td><td>30</td><td>34.5</td><td>39</td><td>44.8</td><td>40</td><td>46.0</td><td>27.6</td><td>3.48</td><td>6.4</td><td>7.54</td><td>6.54</td><td>7.25</td><td>3.3</td><td>47</td><td>2</td><td>3</td><td>5</td><td>1</td><td>14</td><td>3</td><td>0</td><td>7</td><td>5</td><td>1392</td><td>43125</td><td>0.11135057471264367</td><td>0.2224316682375118</td><td>1406</td><td>37944</td><td>0.15433854907539118</td><td>0.18567389255419417</td><td>1534</td><td>38846</td><td>0.11864406779661017</td><td>0.20197486535008977</td></tr>\n", "<tr><td>6070</td><td>2011</td><td>8</td><td>CT</td><td>-72.8</td><td>41.8875</td><td>0.0</td><td>11.0</td><td>0.625</td><td>8.0</td><td>106576.40000000001</td><td>42654.75</td><td>1111200</td><td>147300</td><td>149231.15000000002</td><td>1258500</td><td>6070</td><td>4516</td><td>168</td><td>3.7</td><td>168</td><td>3.7</td><td>187</td><td>4.1</td><td>468</td><td>10.4</td><td>480</td><td>10.6</td><td>572</td><td>12.7</td><td>590</td><td>13.1</td><td>6.6</td><td>1.76</td><td>6.79</td><td>7.64</td><td>6.95</td><td>7.02</td><td>1.55</td><td>3925</td><td>20</td><td>36</td><td>55</td><td>33</td><td>135</td><td>97</td><td>26</td><td>108</td><td>81</td><td>15277</td><td>126868</td><td>0.18367480526281338</td><td>0.6522983201033783</td><td>14996</td><td>110995</td><td>0.1182982128567618</td><td>0.6480204100916565</td><td>15116</td><td>119871</td><td>0.08930934109552792</td><td>0.6536561264822134</td></tr>\n", "<tr><td>6460</td><td>2010</td><td>3</td><td>CT</td><td>-73.0275</td><td>41.2</td><td>1.0</td><td>9.0</td><td>0.725</td><td>41.0</td><td>288366.51</td><td>38191.54</td><td>8399500</td><td>1442600</td><td>326558.05000000005</td><td>9842100</td><td>6460</td><td>14051</td><td>2726</td><td>19.4</td><td>782</td><td>5.6</td><td>1010</td><td>7.2</td><td>2568</td><td>18.3</td><td>3280</td><td>23.3</td><td>3289</td><td>23.4</td><td>4298</td><td>30.6</td><td>-1.1</td><td>2.58</td><td>6.17</td><td>7.67</td><td>7.03</td><td>7.03</td><td>1.51</td><td>9753</td><td>140</td><td>669</td><td>356</td><td>466</td><td>1175</td><td>249</td><td>44</td><td>659</td><td>540</td><td>39185</td><td>90653</td><td>0.25606737271915275</td><td>0.4639441629475521</td><td>38233</td><td>80776</td><td>0.24026364658802604</td><td>0.41461146321300635</td><td>37953</td><td>77369</td><td>0.23191842542091534</td><td>0.4079910164457002</td></tr>\n", "</table>"], "text/plain": ["<Table length=10>\n", "reportedZipcode yearOfLoss ... 2010.renter_fraction 2010.educated_fraction\n", "     int64        int64    ...       float64               float64        \n", "--------------- ---------- ... -------------------- ----------------------\n", "           1001       2005 ...  0.18601841196777905    0.24933451641526175\n", "           1845       1998 ...   0.1708757202817102     0.5528379997821113\n", "           2045       2015 ...   0.2137884355057049      0.378358301349511\n", "           2190       1996 ...  0.20839515331355093    0.34660805103008363\n", "           2664       1991 ...   0.2678309976992261    0.28992532424996725\n", "           2893       1979 ...  0.43344524705802556    0.21516705947594622\n", "           3842       1990 ...  0.25795902022291933     0.3966735223106501\n", "           4747       1987 ...  0.11864406779661017    0.20197486535008977\n", "           6070       2011 ...  0.08930934109552792     0.6536561264822134\n", "           6460       2010 ...  0.23191842542091534     0.4079910164457002"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["comb[::1000][:10]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["181 of 20523 zipcodes dont have 2010 census values\n", "559 of 20523 zipcodes dont have 2014 census values\n", "755 of 20523 zipcodes dont have 2018 census values\n", "947 of 20523 zipcodes dont have census values\n"]}], "source": ["for year in [2010, 2014, 2018]: \n", "    print('%i of %i zipcodes dont have %s census values' % \n", "      (len(np.unique(comb['zipcode'][comb['%s.population' % year].mask])), len(np.unique(comb['zipcode'])), year))\n", "print('%i of %i zipcodes dont have census values' % \n", "      (len(np.unique(comb['zipcode'][comb['2010.population'].mask | comb['2014.population'].mask | comb['2018.population'].mask])), len(np.unique(comb['zipcode']))))    "]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["(20.0, 50.0)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(20,10))\n", "sub = fig.add_subplot(111)\n", "sub.scatter(comb['longitude'], comb['latitude'], s=1, c='k', rasterized=True, label='all')\n", "sub.scatter(comb['longitude'][comb['2010.population'].mask], comb['latitude'][comb['2010.population'].mask], s=3, c='C0', rasterized=True, label='no 2010 census')\n", "sub.scatter(comb['longitude'][comb['2014.population'].mask], comb['latitude'][comb['2014.population'].mask], s=3, c='C1', rasterized=True, label='no 2014 census')\n", "sub.scatter(comb['longitude'][comb['2018.population'].mask], comb['latitude'][comb['2018.population'].mask], s=3, c='C2', rasterized=True, label='no 2018 census')\n", "sub.legend(loc='lower left', markerscale=20, handletextpad=0., fontsize=25)\n", "sub.set_xlabel('longitude', fontsize=25)\n", "sub.set_xlim(-130., -60)\n", "sub.set_ylabel('latitude', fontsize=25)\n", "sub.set_ylim(20, 50)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["combined = comb[~(comb['2010.population'].mask | comb['2014.population'].mask | comb['2018.population'].mask)]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["combined.write('/Users/<USER>/data/noah/fema.zipcode.fsf.acs.csv', format='csv', overwrite=True)"]}], "metadata": {"kernelspec": {"display_name": "noah", "language": "python", "name": "noah"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}